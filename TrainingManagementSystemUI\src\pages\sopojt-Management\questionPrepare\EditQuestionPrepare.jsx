import React, { useState, useEffect, useContext, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styles from './questionPrepare.module.css';
import { updateQuestion } from '../../../services/sopojt-Management/QuestionPrepareService';
import { QuestionPrepareContext } from '../../../context/sopOjt-Management/QuestionPrepareContext';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';

const MAX_QUESTION_LENGTH = 200;
const MAX_OPTION_LENGTH = 100;

const EditQuestionPrepare = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { editModeData, loading: contextLoading, error: contextError, loadQuestionForEdit, clearEditModeData } = useContext(QuestionPrepareContext);
const {
    questions,
    selectedQuestion,
    setSelectedQuestion,
    loadQuestions,
    removeQuestion,
    addQuestion
  } = useContext(QuestionPrepareContext);

  const [loading, setLoading] = useState(false);
  const [questionsForms, setQuestionsForms] = useState([]);
  const [documentInfo, setDocumentInfo] = useState({
    linkedDocument: '',
    totalQuestions: 1,
    preparationID: null
  });
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');
  const initialFormRef = useRef(null);
  const [deletedQuestionIds, setDeletedQuestionIds] = useState([]);
  const [deletedOptionIds, setDeletedOptionIds] = useState([]);

  // Load question data when component mounts or id changes
  useEffect(() => {

    console.log(questions,
      selectedQuestion,
      setSelectedQuestion,
      loadQuestions,
      removeQuestion,
      addQuestion);
    // console.log(editModeData, loading, error, loadQuestionForEdit, clearEditModeData,"kajsojoajs");
    if (id) {

      loadQuestionForEdit(id);
    }
  }, [id, loadQuestionForEdit]);

  // Update local state when editModeData changes
  useEffect(() => {
    if (editModeData && editModeData.documentID !== undefined && editModeData.requiredQuestions !== undefined) {
      setDocumentInfo({
        linkedDocument: editModeData.documentID,
        totalQuestions: editModeData.requiredQuestions,
        preparationID: editModeData.preparationID
      });
      setQuestionsForms(editModeData.questions.map(q => ({ ...q, options: q.options.map(opt => ({ ...opt })) })));
      // Store a deep copy of the initial state
      initialFormRef.current = JSON.stringify({
        documentInfo: {
          linkedDocument: editModeData.documentID,
          totalQuestions: editModeData.requiredQuestions,
          preparationID: editModeData.preparationID
        },
        questionsForms: editModeData.questions.map(q => ({ ...q, options: q.options.map(opt => ({ ...opt })) }))
      });
    } else if (!contextLoading && !contextError && !editModeData && id) {
        // If in edit mode (id exists), context is not loading or erroring,
        // and editModeData is not set yet, try to load the data.
        // This might happen on direct access or refresh.
        // We need a way to get documentID and requiredQuestions here.
        // This highlights the need to ensure these are passed or derivable.
        // For now, let's rely on the context's loadQuestionForEdit which now accepts these.
        // The grid must ensure these are in sessionStorage or passed correctly on navigation.
         console.log("editModeData is not fully populated, waiting...");
    }
  }, [editModeData, contextLoading, contextError, id]); // Depend on editModeData and loading/error states

  const handleOptionChange = (questionIndex, optionIndex, value) => {
    setQuestionsForms(prev => prev.map((q, idx) => {
      if (idx !== questionIndex) return q;
      const newOptions = q.options.map((opt, oidx) => oidx === optionIndex ? { ...opt, optionText: value } : opt);
      return { ...q, options: newOptions };
    }));
  };

  const addOption = (questionIndex) => {
    const updatedQuestions = [...questionsForms];
    if (updatedQuestions[questionIndex].options.length < 4) {
      updatedQuestions[questionIndex].options.push({
        optionText: '',
        optionID: 0,
        isCorrect: false
      });
      setQuestionsForms(updatedQuestions);
    } else {
      toast.warning('Maximum 4 options allowed.');
    }
  };

  const removeOption = (questionIndex, optionIndex) => {
  const updatedQuestions = [...questionsForms];
  const optionToRemove = updatedQuestions[questionIndex].options[optionIndex];

  if (updatedQuestions[questionIndex].options.length > 2) {
    if (optionToRemove.optionID && optionToRemove.optionID > 0) {
      setDeletedOptionIds(prev => [...prev, optionToRemove.optionID]);
    }

    const options = updatedQuestions[questionIndex].options.filter((_, i) => i !== optionIndex);

    if (updatedQuestions[questionIndex].correctAnswer === optionToRemove.optionText) {
      updatedQuestions[questionIndex].correctAnswer = '';
    }

    updatedQuestions[questionIndex].options = options;
    setQuestionsForms(updatedQuestions);
  } else {
    toast.warning('Minimum 2 options required.');
  }
};

  const handleQuestionTextChange = (questionIndex, value) => {
    setQuestionsForms(prev => prev.map((q, idx) => idx === questionIndex ? { ...q, questionText: value } : q));
  };

  const handleCorrectAnswerChange = (questionIndex, value) => {
    setQuestionsForms(prev => prev.map((q, idx) => idx === questionIndex ? { ...q, correctAnswer: value } : q));
  };

  const handleMandatoryChange = (questionIndex, checked) => {
    const maxMandatory = parseInt(documentInfo.totalQuestions) || 0;
    const currentMandatoryCount = questionsForms.filter(q => q.mandatory).length;

    // If trying to tick ON and already at max, prevent
    if (checked && currentMandatoryCount >= maxMandatory) {
      toast.warning(`You can only mark up to ${maxMandatory} questions as mandatory.`);
      return;
    }

    setQuestionsForms(prev => prev.map((q, idx) => idx === questionIndex ? { ...q, mandatory: checked } : q));
  };

  const handleMarksChange = (questionIndex, value) => {
    setQuestionsForms(prev => prev.map((q, idx) => idx === questionIndex ? { ...q, marks: value } : q));
  };

  const handleAddQuestion = () => {
    setQuestionsForms([...questionsForms, {
      questionText: '',
      options: [
        { optionText: '', optionID: 0, isCorrect: false },
        { optionText: '', optionID: 0, isCorrect: false }
      ],
      correctAnswer: '',
      mandatory: false,
      marks: 0,
    }]);
  };

  const handleRemoveQuestion = (questionIndex) => {
    // Don't allow removing if it would go below required questions
    if (questionsForms.length <= documentInfo.totalQuestions) {
      toast.warning(`Cannot remove questions. Minimum ${documentInfo.totalQuestions} questions required.`);
      return;
    }

    const questionToRemove = questionsForms[questionIndex];
    if (questionToRemove.questionID && questionToRemove.questionID > 0) {
      setDeletedQuestionIds(prev => [...prev, questionToRemove.questionID]);
    }

    const updatedQuestions = questionsForms.filter((_, index) => index !== questionIndex);
    setQuestionsForms(updatedQuestions);
  };

  // Update the effect that syncs questionsForms with totalQuestions
  useEffect(() => {
    const requiredQuestions = parseInt(documentInfo.totalQuestions) || 0;
    let currentQuestionsCount = questionsForms.length;

    if (requiredQuestions > 0) {
      if (currentQuestionsCount < requiredQuestions) {
        // Add new empty questions (unique objects)
        const newQuestions = Array.from({ length: requiredQuestions - currentQuestionsCount }, () => ({
          questionText: '',
          options: [
            { optionText: '', optionID: 0, isCorrect: false },
            { optionText: '', optionID: 0, isCorrect: false }
          ],
          correctAnswer: '',
          mandatory: false,
          marks: 0,
        }));
        setQuestionsForms([...questionsForms, ...newQuestions]);
      }
      // Do NOT remove extra questions if currentQuestionsCount > requiredQuestions
    }
  }, [documentInfo.totalQuestions]);

  // Helper to check if form changed
  const isFormChanged = () => {
    if (!initialFormRef.current) return false;
    return JSON.stringify({ documentInfo, questionsForms }) !== initialFormRef.current;
  };

  // Modified handleSubmit to show modal if changed
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!isFormChanged()) {
      toast.info('No changes made to update.');
      return;
    }
    setShowReasonModal(true);
  };

  // Confirm update with reason
  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }
    try {
      setLoading(true);
      // ... validation logic (copy from previous handleSubmit) ...
      if (!questionsForms || questionsForms.length === 0) {
        toast.error('Please add at least one question.');
        setLoading(false);
        return;
      }
      for (const question of questionsForms) {
        if (!question.questionText.trim()) {
          toast.error('Please enter text for all questions.');
          setLoading(false);
          return;
        }
        if (!question.marks || question.marks <= 0) {
          toast.error('Please enter valid marks for all questions.');
          setLoading(false);
          return;
        }
        if (question.options.filter(opt => opt.optionText.trim()).length < 2) {
          toast.error('Please provide at least 2 options for all questions.');
          setLoading(false);
          return;
        }
        if (!question.correctAnswer) {
          toast.error('Please select a correct answer for all questions.');
          setLoading(false);
          return;
        }
        const optionTexts = question.options
          .filter(opt => opt.optionText.trim())
          .map(opt => opt.optionText.trim().toLowerCase());
        const uniqueOptions = new Set(optionTexts);
        if (uniqueOptions.size !== optionTexts.length) {
          toast.error('Options cannot be the same within a question.');
          setLoading(false);
          return;
        }
      }
      // Get user information from session storage
      const userDataStr = sessionStorage.getItem('userData');
      const plantId = sessionStorage.getItem('plantId');
      const userId = sessionStorage.getItem('userId');
      let userData;
      try {
        userData = userDataStr ? JSON.parse(userDataStr) : null;
      } catch {
        userData = null;
      }
      const missingInfo = [];
      if (!userData) missingInfo.push('userData');
      if (!plantId) missingInfo.push('plantId');
      if (!userId) missingInfo.push('userId');
      if (missingInfo.length > 0) {
        toast.error(`Missing required user information: ${missingInfo.join(', ')}. Please log in again.`);
        setLoading(false);
        return;
      }
      const currentDate = new Date().toISOString();
      const userEmail = userData.emailID || userData.loginID;
      const userName = `${userData.firstName} ${userData.lastName}`.trim();
      if (!editModeData?.preparationID) {
        toast.error('Preparation ID is missing. Cannot update questions.');
        setLoading(false);
        return;
      }
      const questionDataToSend = {
        preparationID: editModeData.preparationID,
        documentID: editModeData.documentID,
        requiredQuestions: parseInt(documentInfo.totalQuestions),
        modifiedBy: userEmail || userId.toString(),
        plantID: parseInt(plantId),
        electronicSignature: userName || userData.employeeID,
        signatureDate: currentDate,
        reasonForChange: reasonForChange, // Pass reason for change
        deletedQuestionIDs: deletedQuestionIds,
        deletedOptionIDs: deletedOptionIds,
        questions: questionsForms.map(q => ({
          questionID: q.questionID != null ? Number(q.questionID) : 0,
          questionText: String(q.questionText).trim(),
          isMandatory: Boolean(q.mandatory),
          marks: Number(q.marks) || 0,
          modifiedBy: userEmail || userId.toString(),
          electronicSignature: userName || userData.employeeID,
          signatureDate: currentDate,
          options: q.options
            .filter(opt => opt.optionText.trim())
            .map((opt, optIndex) => ({
              optionID: opt.optionID > 0 ? Number(opt.optionID) : 0,
              optionText: String(opt.optionText).trim(),
              isCorrect: Boolean(opt.optionText === q.correctAnswer),
              displayOrder: Number(optIndex + 1)
            }))
        }))
      };
      const response = await updateQuestion(questionDataToSend);
      if (response?.header?.errorCount === 0) {
        toast.success(response?.header?.messages?.[0]?.messageText || 'Question updated successfully', {
          onClose: () => {
            clearEditModeData();
            navigate('/document-management/questioner-preparation');
          }
        });
      } else {
        toast.error(response?.header?.messages?.[0]?.messageText || 'Failed to update questions');
      }
    } catch (e) {
      toast.error(e.message || 'Failed to update questions');
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const getFilledOptions = (questionIndex) => {
    return questionsForms[questionIndex].options
      .map((opt, idx) => ({ opt: opt.optionText, idx }))
      .filter(o => o.opt.trim() !== '');
  };

  if (contextLoading || !editModeData || editModeData.documentID === undefined || editModeData.requiredQuestions === undefined) {
    return <div className={styles.loading}>Loading...</div>;
  }

  if (contextError) {
    return <div className={styles.error}>{contextError}</div>;
  }

  return (
    <div className={styles.container}>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the questions for document "{editModeData?.documentName}"</p>
              <div className={styles.reasonInput}>
                {/* <label htmlFor="reasonForChange">Reason for change:</label> */}
                <br />
                <textarea
                  id="reasonForChange"
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={() => setShowReasonModal(false)}
        />
      )}
      <form className={styles.form} onSubmit={handleSubmit}>
        <div className={styles.formContent}>
          <h3 className={styles.sectionHeading}>Questioner Preparation</h3>

          {/* Document Details Section */}
          <h4 style={{ color: '#00376e', marginBottom: '1rem' }}>Document Details</h4>
          <div className={styles.formGrid}>
            {/* Linked Document */}
            <div className={styles.row}>
              <label>Linked Document <span className={styles.required}>*</span></label>
              <select value={documentInfo.linkedDocument} disabled>
                <option>
                  {editModeData?.documentName || 'Document'}
                </option>
              </select>
            </div>

            {/* Total Questions Required */}
            <div className={styles.row}>
              <label>Total Questions required in exam <span className={styles.required}>*</span></label>
              <input
                type="text"
                value={documentInfo.totalQuestions}
                onChange={e => {
                  const val = e.target.value.replace(/[^0-9]/g, '');
                  setDocumentInfo(info => ({ ...info, totalQuestions: val }));
                }}
                required
              />
            </div>
          </div>

          {/* Question Forms Section */}
          <div className={styles.formSection}>
             <h4 style={{ color: '#00376e', marginBottom: '1rem' }}>Question Forms</h4>
             {questionsForms.map((q, idx) => (
               <div
                 key={`question-${idx}`}
                 className={styles.questionForm}
               >
                 <div className={styles.questionHeader}>
                   <h5>Question {idx + 1}</h5>
                   <div className={styles.actions}>
                     {questionsForms.length > documentInfo.totalQuestions ? (
                       <button type="button" onClick={() => handleRemoveQuestion(idx)}>Remove Question</button>
                     ) : null}
                   </div>
                 </div>

                 {/* Question Text - Full Width */}
                 <div className={styles.row}>
                   <label htmlFor={`questionText-${idx}`}>Question Text <span className={styles.required}>*</span></label>
                   <textarea
                     id={`questionText-${idx}`}
                     value={q.questionText}
                     onChange={(e) => handleQuestionTextChange(idx, e.target.value)}
                     required
                   />
                 </div>

                 <div>
                   <div className={styles.optionsColumn}>
                     <label>Options <span className={styles.required}>*</span></label>
                     <div className={styles.optionsList}>
                       {q.options.map((opt, oidx) => (
                         <div key={`option-${idx}-${oidx}`} className={styles.optionItem}>
                           <input
                             type="text"
                             maxLength={MAX_OPTION_LENGTH}
                             value={opt.optionText}
                             placeholder={`Option ${String.fromCharCode(65 + oidx)}`}
                             onChange={(e) => handleOptionChange(idx, oidx, e.target.value)}
                           />
                           {q.options.length > 2 && (
                             <button
                               type="button"
                               onClick={() => removeOption(idx, oidx)}
                               className={styles.removeOptionButton}
                             >
                               Remove
                             </button>
                           )}
                         </div>
                       ))}
                       {q.options.length < 4 && (
                         <button
                           type="button"
                           onClick={() => addOption(idx)}
                           className={styles.addOptionButton}
                         >
                           Add Option
                         </button>
                       )}
                     </div>
                   </div>
                 </div>
                  <div className={styles.questionSettings}>
                    <div className={styles.row}>
                      <label htmlFor={`correctAnswer-${idx}`}>Correct Answer <span className={styles.required}>*</span></label>
                      <select
                        value={q.correctAnswer}
                        onChange={(e) => handleCorrectAnswerChange(idx, e.target.value)}
                        required
                      >
                        <option value="">Select Correct Option</option>
                        {getFilledOptions(idx).map(({ opt, idx: optIdx }) => (
                          <option key={`correct-${idx}-${optIdx}`} value={opt}>
                            {String.fromCharCode(65 + optIdx)}. {opt}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className={styles.row}>
                      <label htmlFor={`marks-${idx}`}>Marks <span className={styles.required}>*</span></label>
                      <input
                        id={`marks-${idx}`}
                        type="text"
                        value={q.marks}
                        onChange={(e) => handleMarksChange(idx, e.target.value.replace(/[^0-9]/g, ''))}
                        required
                      />
                    </div>

                    <div className={styles.mandatoryRow}>
                      <label>
                        <input
                          type="checkbox"
                          checked={q.mandatory}
                          onChange={(e) => handleMandatoryChange(idx, e.target.checked)}
                        />
                        {" "} Mandatory Question?
                      </label>
                    </div>
                  </div>
               </div>
             ))}
             <div className={styles.addQuestion}>
               <button type="button" onClick={handleAddQuestion}>Add Question</button>
             </div>
          </div>

          <div className={styles.submitRow}>
            <button type="submit" className={styles.primaryBtn} disabled={loading || contextLoading}>
              {loading || contextLoading ? 'Updating...' : 'Update'}
            </button>
            <button
              type="button"
              className={styles.cancelBtn}
              onClick={() => {
                clearEditModeData();
                navigate('/document-management/questioner-preparation');
              }}
              disabled={loading || contextLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      </form>

      {questionsForms.length > 0 && (
        <div className={`${styles.form} ${styles.questionPaperSection}`}>
          <div className={styles.questionPaperTitle}>Question Paper Preview</div>
          {questionsForms.map((q, idx) => (
            <div key={`question-${idx}`} className={styles.questionCardFullWidth}>
              <div className={styles.questionText}>{idx + 1}. {q.questionText}</div>
              <div className={styles.optionsList}>
                {q.options.map((opt, oidx) => (
                  <label key={oidx} className={styles.optionItem} style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                    <input
                      type="radio"
                      name={`question_${idx}_preview`}
                      value={opt.optionText}
                      checked={q.correctAnswer === opt.optionText}
                      disabled
                      style={{marginRight: 8}}
                    />
                    {String.fromCharCode(65 + oidx)}. {opt.optionText}
                  </label>
                ))}
              </div>
              <div style={{marginTop: 8, color: '#127C96', fontWeight: 600}}>
                {q.correctAnswer && (
                  <>
                    Correct Answer: {String.fromCharCode(65 + q.options.findIndex(o => o.optionText === q.correctAnswer))}. {q.correctAnswer}
                  </>
                )}
              </div>
              <div style={{fontSize: '0.95rem', color: '#888'}}>
                Marks: {q.marks} | Mandatory: {q.mandatory ? 'Yes' : 'No'}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EditQuestionPrepare;