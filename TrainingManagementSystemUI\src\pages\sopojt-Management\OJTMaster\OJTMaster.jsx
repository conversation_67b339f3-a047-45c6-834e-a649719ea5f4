import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaEye, FaDownload, FaBan } from 'react-icons/fa';
import Pagination from '../../../components/pagination/Pagination';
import styles from './OJTMaster.module.css';
import { fetchOJTByUserId, deleteOJTById } from '../../../services/sopojt-Management/OJTMasterService';
import Modal from '../../../components/common/Modal';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { downloadFileToBrowserById } from '../../../services/DownloadService';
import FileViewer from '../../../components/common/fileViewer/FileViewer';

const OJTMaster = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [ojtData, setOjtData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedOJT, setSelectedOJT] = useState(null);
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(false);
  const [showFileViewer, setShowFileViewer] = useState(false);
  const [pdfFileId, setPdfFileId] = useState(null);
  const [pdfFileType, setPdfFileType] = useState(null);
  const [pdfFileExtension, setPdfFileExtension] = useState(null);
  const itemsPerPage = 10;

  const tableContainerRef = useRef(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm,
          showOnlyUnderReview: false
        };
        const res = await fetchOJTByUserId(payload);
        setOjtData(res.onJobTrainingMaster || []);
        setTotalRecords(res.totalRecord || 0);
      } catch {
        setOjtData([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  useEffect(() => {
    const checkScroll = () => {
      const container = tableContainerRef.current;
      if (!container) return;
      const { scrollLeft, scrollWidth, clientWidth } = container;
      setShowLeftIndicator(scrollLeft > 0);
      setShowRightIndicator(scrollLeft < scrollWidth - clientWidth - 1);
    };

    checkScroll();

    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScroll);
      window.addEventListener('resize', checkScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      }
    };
  }, [ojtData]);

  const handleScroll = (direction) => {
    const container = tableContainerRef.current;
    if (!container) return;
    const scrollAmount = 200;
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else if (direction === 'right') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1); // Reset to first page on search
  };

  const handleAddOJT = () => {
    navigate('/document-management/ojt-master/add-ojt');
  };

  const handleEdit = (id) => {
    navigate(`/document-management/ojt-master/edit-ojt/${id}`);
  };

  const handleView = (ojt) => {
    setPdfFileId(ojt.onJobTrainingID);
    setPdfFileType('ojt');
    setPdfFileExtension(ojt.associatedDocumentExtention);
    setShowFileViewer(true);
  };

  const handleDownload = async (documentId) => {
    try {
      // console.log(documentId);
      await downloadFileToBrowserById('ojt',documentId);

    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const handleDeactivate = (ojt) => {
    setSelectedOJT(ojt);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedOJT) return;
    setLoading(true);
    try {
      const modifiedBy = sessionStorage.getItem('userID') || 'system';
      const res = await deleteOJTById(selectedOJT.onJobTrainingID, modifiedBy);
      if (res?.header?.errorCount === 0) {
        toast.success(res.header.messages?.[0]?.messageText || 'OJT deactivated successfully.');
        // Refresh data
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: searchTerm,
          showOnlyUnderReview: false
        };
        const fetchRes = await fetchOJTByUserId(payload);
        setOjtData(fetchRes.onJobTrainingMaster || []);
        setTotalRecords(fetchRes.totalRecord || 0);
      } else {
        toast.error(res?.header?.messages?.[0]?.messageText || 'Failed to deactivate OJT.');
      }
    } catch {
      toast.error('An error occurred while deactivating OJT.');
    } finally {
      setLoading(false);
      setShowDeleteModal(false);
      setSelectedOJT(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedOJT(null);
  };

  const getStatusClass = (status) => {
    switch ((status || '').toLowerCase()) {
      case 'underreview':
        return styles.statusDraft;
      case 'approved':
        return styles.statusApproved;
      case 'returned':
        return styles.statusReturn;
      case 'rejected':
        return styles.statusReject;
      case 'inactive':
        return styles.statusReject;
      default:
        return styles.statusDraft;
    }
  };

  const handleTooltipPosition = (event) => {
    const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
    if (tooltip) {
      const rect = event.currentTarget.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
      tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showFileViewer && (
        <FileViewer
          id={pdfFileId}
          type={pdfFileType}
          extension={pdfFileExtension}
          onClose={() => setShowFileViewer(false)}
        />
      )}
      <div className={styles.container}>
        <div className={styles.documentMaster}>
          <div className={styles.panelHeader}>
            <h2>On Job Training Master</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={handleSearch}
                className={styles.searchInput}
              />
              <button onClick={handleAddOJT} className={styles.addDocBtn}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.docTableContainer} ref={tableContainerRef}>
            <table className={styles.docTable}>
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Code</th>
                  <th>Evaluation Type</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="5" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : ojtData.length === 0 ? (
                  <tr>
                    <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                      No OJT records found.
                    </td>
                  </tr>
                ) : (
                  ojtData.map((item) => (
                    <tr key={item.onJobTrainingID}>
                      <td>{item.onJobTrainingTitle}</td>
                      <td>{item.onJobTrainingCode}</td>
                      <td>{item.evaluationType}</td>

                      <td>
                        <div
                          className={styles.tooltipWrapper}
                          onMouseEnter={handleTooltipPosition}
                        >
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(item.ojtStatus)}`}
                          >
                            {item.ojtStatus}
                          </span>
                          <span className={styles.tooltipText}>{item.approvalRemarks || 'No remarks available'}</span>
                        </div>
                      </td>

                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleView(item)}
                            title="View"
                            disabled={!item.associatedDocumentPath}
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleDownload(item.onJobTrainingID)}
                            title="Download"
                            disabled={!item.associatedDocumentPath}
                          >
                            <FaDownload className={styles.downloadIcon} />
                            <span>Download</span>
                          </button>
                          <span className={styles.actionDivider}></span>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleEdit(item.onJobTrainingID)}
                            title="Edit"
                            disabled={
                              item.ojtStatus === 'Approved' ||
                              item.ojtStatus === 'Rejected' 
                            }
                          >
                            <FaEdit className={styles.editIcon} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleDeactivate(item)}
                            title="Deactivate"
                          >
                            <FaBan className={styles.deactivateIcon} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalRecords / itemsPerPage)}
              paginate={handlePageChange}
            />
          </div>
        </div>
      </div>
      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate OJT "${selectedOJT?.onJobTrainingTitle}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
    </>
  );
};

export default OJTMaster;
