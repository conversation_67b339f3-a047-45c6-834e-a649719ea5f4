import React, { useState, useContext, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styles from './questionPrepare.module.css';
import { fetchDocumentsByUserId, createQuestion, fetchQuestionById, updateQuestion, fetchDocumentsWithNoQuestions } from '../../../services/sopojt-Management/QuestionPrepareService';
import { QuestionPrepareContext } from '../../../context/sopOjt-Management/QuestionPrepareContext';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const MAX_QUESTION_LENGTH = 200;
const MAX_OPTION_LENGTH = 100;

const QuestionPrepare = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);

  console.log('QuestionPrepare Component loaded. ID from URL:', id);
  console.log('Is Edit Mode:', isEditMode);

  const {
    questions,
    loading: contextLoading,
    error: contextError,
    addQuestion,
    setError: setContextError
  } = useContext(QuestionPrepareContext);

  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Separate state for document info and questions list
  const [documentInfo, setDocumentInfo] = useState({
    linkedDocument: '',
    totalQuestions: 1,
    documentID: 0,
    ojtid: 0,
    type: '' // 'doc' or 'ojt'
  });

  const [questionsForms, setQuestionsForms] = useState([
    {
      questionText: '',
      options: [
        { optionText: '', optionID: 0, isCorrect: false },
        { optionText: '', optionID: 0, isCorrect: false }
      ],
      correctAnswer: '',
      mandatory: false,
      marks: 0,
    },
  ]);

  // Track selected option for each question in preview
  const [selectedOptions, setSelectedOptions] = useState({});

  const lastQuestionRef = useRef(null);

  const loadDocuments = async () => {
    let isMounted = true;
    try {
      setLoading(true);
      setError(null);

      let response;
      if (isEditMode) {
        setDocuments([]);
        setLoading(false);
        return;
      } else {
        response = await fetchDocumentsWithNoQuestions();
        console.log('Response from fetchDocumentsWithNoQuestions:', response);
      }

      if (isMounted) {
        if (response && Array.isArray(response.questionsNotPrepared)) {
          const documentsList = response.questionsNotPrepared;
          console.log('Documents list:', documentsList);

          // Filter out invalid documents
          const validDocuments = documentsList.filter(doc => {
            const hasValidDocId = doc.documentID !== 0;
            const hasValidojtid = doc.ojtid !== 0;
            return doc && (hasValidDocId || hasValidojtid);
          });

          console.log('Valid documents:', validDocuments);
          setDocuments(validDocuments);

          if (validDocuments.length === 0) {
            setError('No documents found that need questions.');
          }
        } else {
          console.log('Invalid response structure:', response);
          setError('No documents found that need questions.');
        }
      }
    } catch (err) {
      if (isMounted) {
        console.error('Error in loadDocuments:', err);
        setError(err.message || 'Failed to load documents');
      }
    } finally {
      if (isMounted) {
        setLoading(false);
      }
    }
    return () => {
      isMounted = false;
    };
  };

  useEffect(() => {
    if (!isEditMode) {
      const cleanup = loadDocuments();
      return () => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      };
    }
     // In edit mode, documents are not loaded into the dropdown, only question data is loaded.
     // The document linked to the question set is displayed/used from the fetched question data.
     // If there's a requirement to change the linked document in edit mode, additional logic is needed.
  }, [currentPage, searchTerm, isEditMode]);

  useEffect(() => {
    if (isEditMode && id) { // Ensure id exists in edit mode
      loadQuestionData();
    }
  }, [id, isEditMode]);

  useEffect(() => {
    // Scroll to the last added question form
    if (questionsForms.length > 1 && lastQuestionRef.current) {
      lastQuestionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
    }
  }, [questionsForms.length]); // Trigger when the number of questions changes

  const loadQuestionData = async () => {
    console.log('Attempting to load question data for ID:', id);
    try {
      setLoading(true);
      const data = await fetchQuestionById(id);
      console.log('Response from fetchQuestionById:', data);

      if (data.header?.errorCount === 0 && data.questions && data.questions.length > 0) {
        const questionData = data.questions[0]; // Access the first item in the questions array
        setDocumentInfo({
          linkedDocument: questionData.documentID,
          totalQuestions: questionData.requiredQuestions,
        });
        // Store question and option IDs when loading for edit
        setQuestionsForms(questionData.questions.map(q => ({
          questionID: q.questionID, // Store question ID
          questionText: q.questionText,
          options: q.options.map(opt => ({
              optionID: opt.optionID, // Store option ID
              optionText: opt.optionText,
              isCorrect: opt.isCorrect
          })),
          correctAnswer: q.options.find(opt => opt.isCorrect)?.optionText || '',
          mandatory: q.isMandatory,
          marks: q.marks,
        })));
         // Also store the top-level preparationID and documentID
         setDocumentInfo(prev => ({
           ...prev,
           preparationID: data.preparationID, // Store preparation ID (assuming it's in the response)
           linkedDocument: data.documentID // Store document ID (assuming it's in the response)
         }));

      } else {
        console.error('API returned an error or no questions found when fetching question data:', data.header?.messages?.[0]?.messageText || 'No questions array in response.');
        toast.error('Failed to load question data or no questions found.');
        navigate('/document-management/questioner-preparation'); // Navigate back to grid on API error or no questions
      }
    } catch (error) {
      console.error('Error loading question data:', error);
      toast.error('Error loading question data');
      navigate('/document-management/questioner-preparation'); // Navigate back to grid on fetch error
    } finally {
      setLoading(false);
    }
  };

  const handleOptionChange = (questionIndex, optionIndex, value) => {
    const updatedQuestions = [...questionsForms];
    updatedQuestions[questionIndex].options[optionIndex] = {
      ...updatedQuestions[questionIndex].options[optionIndex],
      optionText: value
    };
    setQuestionsForms(updatedQuestions);
  };

  const addOption = (questionIndex) => {
    const updatedQuestions = [...questionsForms];
    if (updatedQuestions[questionIndex].options.length < 4) {
      updatedQuestions[questionIndex].options.push({
        optionText: '',
        optionID: 0,
        isCorrect: false
      });
      setQuestionsForms(updatedQuestions);
    } else {
      toast.warning('Maximum 4 options allowed.');
    }
  };

  const removeOption = (questionIndex, optionIndex) => {
    const updatedQuestions = [...questionsForms];
    if (updatedQuestions[questionIndex].options.length > 2) {
      const options = updatedQuestions[questionIndex].options.filter((_, i) => i !== optionIndex);
      // If the removed option was the correct answer, reset correct answer
      if (updatedQuestions[questionIndex].correctAnswer === updatedQuestions[questionIndex].options[optionIndex].optionText) {
        updatedQuestions[questionIndex].correctAnswer = '';
      }
      updatedQuestions[questionIndex].options = options;
      setQuestionsForms(updatedQuestions);
    } else {
      toast.warning('Minimum 2 options required.');
    }
  };

  const handleQuestionTextChange = (questionIndex, value) => {
    const updatedQuestions = [...questionsForms];
    updatedQuestions[questionIndex].questionText = value;
    setQuestionsForms(updatedQuestions);
  };

  const handleCorrectAnswerChange = (questionIndex, value) => {
    const updatedQuestions = [...questionsForms];
    updatedQuestions[questionIndex].correctAnswer = value;
    setQuestionsForms(updatedQuestions);
  };

   const handleMandatoryChange = (questionIndex, checked) => {
    const maxMandatory = parseInt(documentInfo.totalQuestions) || 0;
    const currentMandatoryCount = questionsForms.filter(q => q.mandatory).length;

    // If trying to tick ON and already at max, prevent
    if (checked && currentMandatoryCount >= maxMandatory) {
      toast.warning(`You can only mark up to ${maxMandatory} questions as mandatory.`);
      return;
    }

    const updatedQuestions = [...questionsForms];
    updatedQuestions[questionIndex].mandatory = checked;
    setQuestionsForms(updatedQuestions);
  };

  const handleMarksChange = (questionIndex, value) => {
    const updatedQuestions = [...questionsForms];
    updatedQuestions[questionIndex].marks = value;
    setQuestionsForms(updatedQuestions);
  };

  const handleAddQuestion = () => {
    setQuestionsForms([...questionsForms, {
      questionText: '',
      options: [
        { optionText: '', optionID: 0, isCorrect: false },
        { optionText: '', optionID: 0, isCorrect: false }
      ],
      correctAnswer: '',
      mandatory: false,
      marks: 0,
    }]);
  };

   const handleRemoveQuestion = (questionIndex) => {
    // Don't allow removing if it would go below required questions
    if (questionsForms.length <= documentInfo.totalQuestions) {
      toast.warning(`Cannot remove questions. Minimum ${documentInfo.totalQuestions} questions required.`);
      return;
    }

    const updatedQuestions = questionsForms.filter((_, index) => index !== questionIndex);
    setQuestionsForms(updatedQuestions);
     // Also remove the selected option for this question from selectedOptions state
     setSelectedOptions(prev => {
       const newState = { ...prev };
       delete newState[questionIndex];
       return newState;
     });
  };

  // Update the effect that syncs questionsForms with totalQuestions
  useEffect(() => {
    const requiredQuestions = parseInt(documentInfo.totalQuestions) || 0;
    let currentQuestionsCount = questionsForms.length;

    if (requiredQuestions > 0) {
      if (currentQuestionsCount < requiredQuestions) {
        // Add new empty questions (unique objects)
        const newQuestions = Array.from({ length: requiredQuestions - currentQuestionsCount }, () => ({
          questionText: '',
          options: [
            { optionText: '', optionID: 0, isCorrect: false },
            { optionText: '', optionID: 0, isCorrect: false }
          ],
          correctAnswer: '',
          mandatory: false,
          marks: 0,
        }));
        setQuestionsForms([...questionsForms, ...newQuestions]);
      }
      // Do NOT remove extra questions if currentQuestionsCount > requiredQuestions
    }
  }, [documentInfo.totalQuestions]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    let isMounted = true;
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setContextError(null);

      // Get user information from session storage
      const userDataStr = sessionStorage.getItem('userData');
      const plantId = sessionStorage.getItem('plantId');
      const userId = sessionStorage.getItem('userId');

      // Parse userData if it exists
      let userData;
      try {
        userData = userDataStr ? JSON.parse(userDataStr) : null;
      } catch (err) {
        console.error('Error parsing userData:', err);
        userData = null;
      }

      // Check which specific user information is missing
      const missingInfo = [];
      if (!userData) missingInfo.push('userData');
      if (!plantId) missingInfo.push('plantId');
      if (!userId) missingInfo.push('userId');

      if (missingInfo.length > 0) {
        throw new Error(`Missing required user information: ${missingInfo.join(', ')}. Please log in again.`);
      }

      // Extract user information from userData
      const userEmail = userData.emailID || userData.loginID;
      const userName = `${userData.firstName} ${userData.lastName}`.trim();

      // Validate document info
      if (!isEditMode && !documentInfo.linkedDocument) {
        throw new Error('Please select a document');
      }

      // Get the selected document ID based on type
      const [type, id] = documentInfo.linkedDocument.split('-');
      const selectedDoc = documents.find(doc =>
        (type === 'doc' && doc.documentID === Number(id)) ||
        (type === 'ojt' && doc.ojtid === Number(id))
      );

      if (!isEditMode && !selectedDoc) {
        throw new Error('Selected document not found. Please select a valid document.');
      }

      const currentDate = new Date().toISOString();

      // Create question data according to API requirements
      const questionDataToSend = {
        preparationID: isEditMode ? parseInt(id) : 0,
        documentID: type === 'doc' ? Number(id) : 0,
        ojtid: type === 'ojt' ? Number(id) : 0,
        requiredQuestions: parseInt(documentInfo.totalQuestions),
        createdBy: userEmail || userId.toString(), // Add createdBy for create
        modifiedBy: userEmail || userId.toString(), // Keep modifiedBy for update
        plantID: parseInt(plantId),
        electronicSignature: userName || userData.employeeID,
        signatureDate: currentDate,
        questions: questionsForms.map(q => ({
          questionID: isEditMode ? q.questionID : 0,
          questionText: q.questionText.trim(),
          isMandatory: q.mandatory,
          marks: parseInt(q.marks) || 0,
          createdBy: userEmail || userId.toString(), // Add createdBy for create
          modifiedBy: userEmail || userId.toString(), // Keep modifiedBy for update
          electronicSignature: userName || userData.employeeID,
          signatureDate: currentDate,
          options: q.options
            .filter(opt => opt.optionText.trim())
            .map((opt, optIndex) => ({
              optionID: isEditMode ? opt.optionID : 0,
              optionText: opt.optionText.trim(),
              isCorrect: opt.optionText === q.correctAnswer,
              displayOrder: Number(optIndex + 1)
            }))
        }))
      };

      console.log('Sending question data:', questionDataToSend);

      let response;
      if (isEditMode) {
        response = await updateQuestion(questionDataToSend);
      } else {
        response = await createQuestion(questionDataToSend);
      }

      if (isMounted) {
        if (response?.header?.errorCount === 0) {
          const apiMessage = response?.header?.messages?.[0]?.messageText || 'Questions prepared successfully!';
          toast.success(apiMessage);

          // Reset form after successful submission
          if (!isEditMode) {
            setDocumentInfo({
              linkedDocument: '',
              totalQuestions: 1,
              documentID: 0,
              ojtid: 0,
              type: ''
            });
            setQuestionsForms([
              {
                questionText: '',
                options: [
                  { optionText: '', optionID: 0, isCorrect: false },
                  { optionText: '', optionID: 0, isCorrect: false }
                ],
                correctAnswer: '',
                mandatory: false,
                marks: 0,
                questionID: 0
              },
            ]);
            setSelectedOptions({});
            // Reload documents after successful creation
            loadDocuments();
          }

          // Add a delay before navigating to allow the toast to be seen
          setTimeout(() => {
            navigate('/document-management/questioner-preparation');
          }, 1500);
        } else {
          const apiError = response?.header?.messages?.[0]?.messageText || 'Failed to prepare questions';
          toast.error(apiError);
        }
      }
    } catch (err) {
      if (isMounted) {
        console.error('Error in handleSubmit:', err);
        // Display error toast for fetch errors or thrown errors
        toast.error(err.message || `Failed to ${isEditMode ? 'update' : 'create'} questions`);
        setError(err.message || `Failed to ${isEditMode ? 'update' : 'create'} questions`);
        setContextError(err.message || `Failed to ${isEditMode ? 'update' : 'create'} questions`);
      }
    } finally {
      if (isMounted) {
        setLoading(false);
      }
    }
  };

  const handleSaveAndAddAnother = async (e) => {
    e.preventDefault();
    // With the new structure, Save & Add Another doesn't make sense in the same way.
    // The primary action is saving all questions. Adding another question is separate.
    // We can either remove this button or change its behavior. Let's remove it for now.
    await handleSubmit(e);
  };

  // Only show non-empty options in correct answer dropdown for a specific question index
   const getFilledOptions = (questionIndex) => {
    return questionsForms[questionIndex].options
      .map((opt, idx) => ({ opt: opt.optionText, idx }))
      .filter(o => o.opt.trim() !== '');
  };

  // Handle option select in preview (needs adjustment to use questionsForms)
  const handlePreviewOptionSelect = (qIdx, opt) => {
    setSelectedOptions(prev => ({ ...prev, [qIdx]: opt }));
  };

  console.log('Documents state before rendering dropdown:', documents); // Log documents state
  console.log('Questions Forms state before rendering:', questionsForms); // Log questions state

  // Add new useEffect for initialization
  useEffect(() => {
    // Reset scroll position
    window.scrollTo(0, 0);

    // Force a reflow to ensure proper height calculation
    const mainContent = document.querySelector(`.${styles.mainContentBg}`);
    if (mainContent) {
      mainContent.style.display = 'none';
      mainContent.offsetHeight; // Force reflow
      mainContent.style.display = 'flex';
    }

    return () => {
      // Cleanup
      if (mainContent) {
        mainContent.style.display = '';
      }
    };
  }, []); // Run only on mount

  return (
    <div className={styles.container}>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <form className={styles.form} onSubmit={handleSubmit}>
        <div className={styles.formContent}>
          <h3 className={styles.sectionHeading}>
            {isEditMode ? 'Edit Questioner Preparation' : 'Add Questioner Preparation'}
          </h3>
        {success && (
          <div style={{
            padding: '10px',
            backgroundColor: '#4caf50',
            color: 'white',
            borderRadius: '4px',
            marginBottom: '20px'
          }}>
            {success}
          </div>
        )}
        {(error || contextError) && (
          <div style={{
            padding: '10px',
            backgroundColor: '#f44336',
            color: 'white',
            borderRadius: '4px',
            marginBottom: '20px'
          }}>
            {error || contextError}
          </div>
        )}

          {/* Document and Total Questions Section */}
          {!isEditMode && (
            <>
              <h4 style={{ color: '#00376e', marginBottom: '1rem' }}>Document Details</h4>
              <div className={styles.formGrid}>
                {/* Linked Document */}
                <div className={styles.row}>
                  <label>Linked Document <span className={styles.required}>*</span></label>
                   <select
                     value={documentInfo.linkedDocument}
                     onChange={(e) => {
                       const selectedValue = e.target.value;
                       if (!selectedValue) {
                         setDocumentInfo({
                           ...documentInfo,
                           linkedDocument: '',
                           type: '',
                           documentID: 0,
                           ojtid: 0
                         });
                         return;
                       }

                       console.log('Selected document value:', selectedValue);
                       const [type, id] = selectedValue.split('-');
                       const selectedDoc = documents.find(doc =>
                         (type === 'doc' && doc.documentID === Number(id)) ||
                         (type === 'ojt' && doc.ojtid === Number(id))
                       );
                       console.log('Selected document:', selectedDoc);

                       if (selectedDoc) {
                         setDocumentInfo({
                           ...documentInfo,
                           linkedDocument: selectedValue,
                           type: type,
                           documentID: type === 'doc' ? Number(id) : 0,
                           ojtid: type === 'ojt' ? Number(id) : 0
                         });
                       }
                     }}
                     required
                     disabled={loading || documents.length === 0}
                   >
                     <option key="select-doc" value="">Select Document</option>
                     {documents.filter(doc => doc && (doc.documentID || doc.ojtid)).map((doc) => {
                       const value = doc.documentID !== 0
                         ? `doc-${doc.documentID}`
                         : `ojt-${doc.ojtid}`;

                       return (
                         <option
                           key={value}
                           value={value}
                         >
                           {doc.documentName || 'Unnamed Document'}
                         </option>
                       );
                     })}
                   </select>
                  {loading && <small>Loading documents...</small>}
                  {error && (
                    <small style={{color: '#e53935'}}>
                      {error}
                    </small>
                  )}
                  {!loading && documents.length === 0 && !error && (
                    <small style={{color: '#e53935'}}>
                      No documents available that require questions. Please ensure documents are approved and do not have questions prepared yet.
                    </small>
                  )}
                </div>

                {/* Total Questions Required */}
                <div className={styles.row}>
                  <label>Total Questions required in exam <span className={styles.required}>*</span></label>
                  <input
                    type="text"
                    min="1"
                    value={documentInfo.totalQuestions}
                    onChange={(e) => setDocumentInfo({ ...documentInfo, totalQuestions: e.target.value.replace(/[^0-9]/g, '') })}
                    required
                  />
                </div>
              </div>
            </>
          )}

        {/* Question Forms Section */}
        <div className={styles.formSection}>
           <h3>Question Forms</h3>
           {questionsForms.map((q, idx) => (
             <div
               key={`question-${idx}`}
               className={styles.questionForm}
               ref={idx === questionsForms.length - 1 ? lastQuestionRef : null}
             >
               <div className={styles.questionText}>
                 <label htmlFor={`questionText-${idx}`}>Question Text <span className={styles.required}>*</span></label>
                  <textarea
                   id={`questionText-${idx}`}
                   value={q.questionText}
                   onChange={(e) => handleQuestionTextChange(idx, e.target.value)}
                    required
                  />
                </div>
               <div className={styles.optionsList}>
                 {q.options.map((opt, oidx) => (
                   <div key={`option-${idx}-${oidx}`} className={styles.optionItem}>
                      <input
                        type="text"
                        maxLength={MAX_OPTION_LENGTH}
                        value={opt.optionText}
                        placeholder={`Option ${String.fromCharCode(65 + oidx)}`}
                       onChange={(e) => handleOptionChange(idx, oidx, e.target.value)}
                      />
                      {q.options.length > 2 && (
                        <button
                          type="button"
                          onClick={() => removeOption(idx, oidx)}
                          className={styles.removeOptionButton}
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  ))}
                   {q.options.length < 4 && (
                     <button
                       type="button"
                       onClick={() => addOption(idx)}
                       className={styles.addOptionButton}
                     >
                       Add Option
                     </button>
                   )}
                </div>
               <div className={styles.correctAnswer}>
                 <label htmlFor={`correctAnswer-${idx}`}>Correct Answer <span className={styles.required}>*</span></label>
                  <select
                   value={q.correctAnswer}
                   onChange={(e) => handleCorrectAnswerChange(idx, e.target.value)}
                  >
                     <option value="">Select Correct Option</option>
                     {getFilledOptions(idx).map(({ opt, idx: optIdx }) => (
                      <option key={`correct-${idx}-${optIdx}`} value={opt}>
                         {String.fromCharCode(65 + optIdx)}. {opt}
                       </option>
                     ))}
                  </select>
                </div>
               <div className={styles.mandatory}>
                 <input
                   type="checkbox"
                   checked={q.mandatory}
                   onChange={(e) => handleMandatoryChange(idx, e.target.checked)}
                 />
                 {" "} Mandatory Question?
               </div>
               <div className={styles.marks}>
                 <label htmlFor={`marks-${idx}`}>Marks <span className={styles.required}>*</span></label>
                 <input
                   id={`marks-${idx}`}
                   type="text"
                   value={q.marks}
                   onChange={(e) => handleMarksChange(idx, e.target.value.replace(/[^0-9]/g, ''))}
                 />
               </div>
               <div className={styles.actions}>
                 {questionsForms.length > documentInfo.totalQuestions ? (
                   <button type="button" onClick={() => handleRemoveQuestion(idx)}>Remove Question</button>
                 ) : null}
               </div>
             </div>
           ))}
           <div className={styles.addQuestion}>
             <button type="button" onClick={handleAddQuestion}>Add Question</button>
           </div>
        </div>

          <div className={styles.submitRow}>
            <button type="submit" className={styles.primaryBtn} disabled={loading}>
              {loading ? 'Saving...' : isEditMode ? 'Update' : 'Submit'}
            </button>
            <button
              type="button"
              className={styles.cancelBtn}
              onClick={() => navigate('/document-management/questioner-preparation')}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
      {/* Question Paper Section */}
      {questionsForms.length > 0 && (
        <div className={styles.questionPaperSection}>
          <div className={styles.questionPaperTitle}>Question Paper Preview</div>
          {questionsForms.map((q, idx) => (
            <div key={`question-${idx}`} className={styles.questionCard}>
              <div className={styles.questionText}>{idx + 1}. {q.questionText}</div>
              <div className={styles.optionsList}>
                {q.options.map((opt, oidx) => (
                  <label key={oidx} className={styles.optionItem} style={{display: 'flex', alignItems: 'center', cursor: 'pointer'}}>
                    <input
                      type="radio"
                      name={`question_${idx}_preview`}
                      value={opt.optionText}
                      checked={q.correctAnswer === opt.optionText}
                      disabled
                      style={{marginRight: 8}}
                    />
                    {String.fromCharCode(65 + oidx)}. {opt.optionText}
                  </label>
                ))}
              </div>
                <div style={{marginTop: 8, color: '#127C96', fontWeight: 600}}>
                {q.correctAnswer && (
                  <>
                    Correct Answer: {String.fromCharCode(65 + q.options.findIndex(o => o.optionText === q.correctAnswer))}. {q.correctAnswer}
                  </>
                )}
                </div>
              <div style={{fontSize: '0.95rem', color: '#888'}}>
                Marks: {q.marks} | Mandatory: {q.mandatory ? 'Yes' : 'No'}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default QuestionPrepare;
