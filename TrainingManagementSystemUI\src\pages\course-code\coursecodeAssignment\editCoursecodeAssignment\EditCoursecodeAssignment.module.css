.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 2.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: #333;
  box-sizing: border-box;
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.required {
  color: red;
  margin-left: 4px;
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row input:disabled {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

/* React Select Styles */
.reactSelect :global(.react-select__control) {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 2px 5px;
  font-size: 14px;
  color: black;
  min-height: 38px;
}

.reactSelect :global(.react-select__menu) {
  color: #001b36;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.reactSelect :global(.react-select__option) {
  color: #001b36;
  font-size: 14px;
  padding: 8px 12px;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
  color: #222;
}

.modalContent h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.reasonTextarea {
  width: 100%;
  padding: 10px;
  border: 1.5px solid #127C96;
  border-radius: 5px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.modalButtons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.actionButton {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cancelButton {
  padding: 12px 25px;
  font-size: 1rem;
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #ccc;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.cancelButton:hover {
  background-color: #d5d5d5;
  border-color: #b0b0b0;
}

/* Document and OJT Selection Section */
.documentOjtLayout {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  height: 450px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background-color: #f8f9fa;
  overflow: hidden;
}

.availableOjtColumn {
  flex: 0 0 40%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
  overflow: hidden;
}

.availableOjtColumn h4 {
  margin: 0;
  color: #127C96;
  font-size: 0.9rem;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  text-align: center;
  flex-shrink: 0;
}

.selectedOjtColumn {
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: 100%;
  overflow: hidden;
}

.layoutDivider {
  width: 1px;
  background-color: #dee2e6;
  flex-shrink: 0;
}

.documentOjtSearch {
  flex-shrink: 0;
}

.documentOjtSearch input[type="text"] {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
  box-sizing: border-box;
}

.documentOjtList {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  padding: 0.5rem;
  position: relative;
}

.loadingIndicator {
  text-align: center;
  padding: 10px;
  color: #6c757d;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #e9ecef;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
}

.documentOjtItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.documentOjtItem:last-child {
  border-bottom: none;
}

.documentOjtItem:hover {
  background-color: #e9ecef;
}

.addItemButton {
  background: none;
  border: none;
  color: #28a745;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: color 0.2s ease;
}

.addItemButton:hover {
  color: #218838;
}

.noResults,
.noSelected {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-size: 0.95rem;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  margin: 0;
}

.selectedItemsContainerheadings {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  padding: 10px;
  flex-shrink: 0;
}

.selectedItemsContainerheadings h4 {
  margin: 0;
  color: #127C96;
  font-size: 0.9rem;
}

.selectedItemsContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  flex: 1;
  overflow-y: auto;
  box-sizing: border-box;
}

.selectedItemBubble {
  background-color: #f8f9fa;
  color: #333;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 0.9rem;
  box-sizing: border-box;
  position: relative;
}

.selectedItemBubble .itemLabel {
  font-weight: 600;
  color: #127C96;
  margin-bottom: 8px;
}

.removeItemButton {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.2rem;
  transition: color 0.2s ease;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.removeItemButton:hover {
  color: #c82333;
}

.frequencyOptions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.frequencyLabel {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.frequencyLabel:hover {
  background-color: #e9ecef;
}

.frequencyOptions input[type="radio"] {
  margin: 0;
  cursor: pointer;
  transform: scale(1.1);
}

.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

/* User Selection Panel Styles */
.userSelectionPanel {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  height: 500px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
}

.userGridColumn {
  flex: 0 0 70%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  overflow: hidden;
}

/* Divider between columns */
.userSelectionDivider {
  width: 1px;
  background-color: #dee2e6;
  flex-shrink: 0;
}

.selectedUsersColumn {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.userGridColumn h4,
.selectedUsersColumn h4 {
  margin: 0;
  color: #127C96;
  font-size: 1rem;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  text-align: center;
  flex-shrink: 0;
}

.searchFilters {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.searchInput {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

.userGrid {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
}

.userGridHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 80px;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.userGridBody {
  flex: 1;
  overflow-y: auto;
}

.userGridRow {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 80px;
  gap: 1rem;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.userGridRow:hover {
  background-color: #f8f9fa;
}

.addUserBtn {
  background: none;
  border: none;
  color: #28a745;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.2rem;
}

.addUserBtn:hover {
  color: #218838;
}

.noUsers {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
  font-size: 0.9rem;
}

.loadingIndicator {
  text-align: center;
  padding: 1rem;
  color: #6c757d;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #e9ecef;
}

.selectedUsersList {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
}

.selectedUserItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.selectedUserItem:last-child {
  margin-bottom: 0;
}

.userInfo {
  flex: 1;
}

.userName {
  font-weight: 600;
  color: #127C96;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.userDepartment {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.userDesignation {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.userRole {
  font-size: 0.8rem;
  color: #495057;
}

.removeUserBtn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-weight: bold;
  font-size: 1rem;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 0.2rem;
}

.removeUserBtn:hover {
  color: #c82333;
}

.noSelectedUsers {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
  font-size: 0.9rem;
}

.itemLabel {
  font-weight: 600;
}

.itemDescription {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }

  .form {
    padding: 20px;
    margin: 0;
    max-width: 100%;
    border-radius: 0;
  }

  .documentOjtLayout {
    flex-direction: column;
    gap: 1.5rem;
    height: auto;
    min-height: 300px;
  }

  .availableOjtColumn,
  .selectedOjtColumn {
    flex: none;
    height: auto;
    min-height: 200px;
  }

  .layoutDivider {
    width: 100%;
    height: 1px;
  }

  .selectedItemBubble {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .selectedItemBubble .itemLabel {
    margin-right: 0;
  }

  .frequencyOptions {
    justify-content: center;
    width: 100%;
  }

  .modalContent {
    width: 95%;
    padding: 1.5rem;
  }

  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }

  .userSelectionPanel {
    flex-direction: column;
    height: auto;
    min-height: 400px;
  }

  .userGridColumn,
  .selectedUsersColumn {
    flex: none;
    height: auto;
    min-height: 200px;
  }

  .searchFilters {
    flex-direction: column;
    gap: 0.5rem;
  }

  .userGridHeader,
  .userGridRow {
    grid-template-columns: 1fr 1fr 1fr 60px;
    gap: 0.5rem;
    font-size: 0.8rem;
  }

  .userGridHeader div:nth-child(4),
  .userGridRow div:nth-child(4) {
    display: none; /* Hide role column on mobile */
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.25rem;
  }

  .form {
    padding: 15px;
  }

  .userGridHeader,
  .userGridRow {
    grid-template-columns: 1fr 1fr 60px;
    gap: 0.5rem;
  }

  .userGridHeader div:nth-child(3),
  .userGridRow div:nth-child(3) {
    display: none; /* Hide designation column on small mobile */
  }

  .selectedUserItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .removeUserBtn {
    align-self: flex-end;
  }
}
