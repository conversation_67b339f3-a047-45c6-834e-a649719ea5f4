import Api from "../Api";

// API function to fetch course code assignments by user ID
export const fetchCourseCodeAssignmentsByUserId = async (payload) => {
  try {
    const response = await Api.post('/coursecodeassignment/getbyuserid', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to fetch approved course codes for dropdown
export const fetchApprovedCourseCodes = async (payload) => {
  try {
    const response = await Api.post('/coursecode/getapprovedbyuserid', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to search users for user selection
export const searchUsersBasicInfo = async (payload) => {
  try {
    const response = await Api.post('/user/searchusersbasicinfo', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to disable/deactivate course code assignment
export const disableCourseCodeAssignment = async (courseCodeID) => {
  try {
    const response = await Api.post('/coursecodeassignment/delete', { courseCodeID });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to create course code assignment
export const createCourseCodeAssignment = async (payload) => {
  try {
    const response = await Api.post('/coursecodeassignment/insert', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to fetch course code assignment details by ID
export const fetchCourseCodeAssignmentById = async (assignmentID) => {
  try {
    const response = await Api.get(`/coursecodeassignment/${assignmentID}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};

// API function to update course code assignment
export const updateCourseCodeAssignment = async (payload) => {
  try {
    const response = await Api.post('/coursecodeassignment/update', payload);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      return error.response.data;
    } else {
      throw error;
    }
  }
};
