import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './EditPlant.module.css';

import { useNavigate, useLocation } from 'react-router-dom';
import { updatePlant } from '../../../../services/systemAdmin/PlantMasterService';
import { PlantContext } from '../../../../context/PlantContext';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../../components/common/Modal';

const EditPlant = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedPlant } = useContext(PlantContext);
  const initialFormDataRef = useRef(null);

  const [formData, setFormData] = useState({
    plantID: selectedPlant?.plantID || '',
    plantName: selectedPlant?.plantName || ''
  });
  const [loading, setLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');

  useEffect(() => {
    if (location.state?.plant) {
      const initialData = {
        plantID: location.state.plant.plantID,
        plantName: location.state.plant.plantName
      };
      setFormData(initialData);
      initialFormDataRef.current = { ...initialData };
    }
  }, [location.state]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!formData.plantName.trim()) {
      toast.error('Plant name is required.');
      return;
    }

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }

    const payload = {
      plantID: Number(formData.plantID),
      plantName: formData.plantName.trim(),
      modifiedBy: 'Admin',
      reasonForChange: reasonForChange.trim(),
      electronicSignature: 'Admin',
      signatureDate: new Date().toISOString().split('T')[0],
    };

    setLoading(true);
    try {
      const response = await updatePlant(payload);
      if (response.header?.errorCount === 0) {
        const successMsg = response.header?.messages?.[0]?.messageText;
        toast.success(successMsg || 'Plant updated successfully!');
        setTimeout(() => navigate('/system-admin/plant-master'), 1500);
      } else {
        const errorMsg = response.header?.messages?.[0]?.messageText;
        toast.error(errorMsg || 'Failed to update plant');
      }
    } catch (error) {
      console.error('Error updating plant:', error);
      toast.error('An error occurred during update');
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  return (
    <>
      <ToastContainer 
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {loading && <div className={styles.loadingBar}></div>}
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          <h3 className={styles.sectionHeading}>Edit Plant</h3>

          <div className={styles.row}>
            <label>Plant Name <span className={styles.required}>*</span></label>
            <input
              type="text"
              name="plantName"
              value={formData.plantName}
              onChange={handleChange}
              placeholder="Enter Plant Name"
              required
            />
          </div>

          <div className={styles.submitRow}>
            <button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update'}
            </button>
            <button type="button" onClick={() => navigate(-1)} disabled={loading}>
              Cancel
            </button>
          </div>
        </form>

        {/* Reason for Change Modal */}
        {showReasonModal && (
          <Modal
            title="Reason for Change"
            message={
              <div>
                <p>Please provide a reason for updating the plant "{formData.plantName}"</p>
                <div className={styles.reasonInput}>
                  <br />
                  <textarea
                    value={reasonForChange}
                    onChange={(e) => setReasonForChange(e.target.value)}
                    placeholder="Please provide a reason for this change..."
                    required
                  />
                </div>
              </div>
            }
            onConfirm={handleConfirmUpdate}
            onCancel={handleCancelUpdate}
          />
        )}
      </div>
    </>
  );
};

export default EditPlant;
