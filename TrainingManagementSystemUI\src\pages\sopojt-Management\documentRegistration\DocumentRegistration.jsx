import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './DocumentRegistration.module.css';
import { FaEdit, FaEye, FaChevronLeft, FaChevronRight, FaDownload } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import Pagination from '../../../components/pagination/Pagination';
import { useNavigate } from 'react-router-dom';
import { DocumentContext } from '../../../context/sopOjt-Management/DocumentContext';
import { fetchDocumentsByUserId, deleteDocument, fetchDocumentById } from '../../../services/sopojt-Management/DocumentRegistrationService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';
import {downloadFileToBrowserById} from '../../../services/DownloadService'
import FileViewer from '../../../components/common/fileViewer/FileViewer';



const getStatusClass = (status) => {
  switch ((status || '').toLowerCase()) {
    case 'underreview':
      return styles.statusDraft;
    case 'approved':
      return styles.statusApproved;
    case 'returned':
      return styles.statusReturn;
    case 'rejected':
      return styles.statusReject;
    default:
      return styles.statusDraft;
  }
};

const handleTooltipPosition = (event) => {
  const tooltip = event.currentTarget.querySelector(`.${styles.tooltipText}`);
  if (tooltip) {
    const rect = event.currentTarget.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    tooltip.style.top = `${rect.top + scrollTop - tooltip.offsetHeight - 10}px`;
    tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
  }
};

const DocumentMaster = () => {
  const navigate = useNavigate();
  const { setDocumentDetails } = useContext(DocumentContext);

  const [documents, setDocuments] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(false);
  const [showFileViewer, setShowFileViewer] = useState(false);
  const [pdfFileId, setPdfFileId] = useState(null);
  const [pdfFileType, setPdfFileType] = useState(null);
  const [pdfFileExtension, setPdfFileExtension] = useState(null);
  const [viewerKey, setViewerKey] = useState(Date.now());
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;

  // Debounced search effect
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm
        };
        const res = await fetchDocumentsByUserId(payload);
        setDocuments(res.documentMasters || []);
        setTotalRecords(res.totalRecord || 0);
      } catch {
        setDocuments([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);



  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener('scroll', () => {});
      window.addEventListener('resize', () => {});
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', () => {});
        window.removeEventListener('resize', () => {});
      }
    };
  }, [documents]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleAddDocClick = () => {
    navigate('/document-management/document-registration/register-document');
  };

  const handleEditDocClick = async (doc) => {
    try {
      const response = await fetchDocumentById(doc.documentID);

      if (response?.documentMaster) {
        setDocumentDetails(response.documentMaster);
        localStorage.setItem('editDocumentData', JSON.stringify(response.documentMaster));
        navigate('/document-management/document-registration/edit-document');
      } else {
        toast.error('Failed to fetch document details');
      }
    } catch (error) {
      console.error('Error fetching document details:', error);
      toast.error('Failed to prepare document for editing');
    }
  };

  const handleDeleteClick = (doc) => {
    setSelectedDoc(doc);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      const userId = sessionStorage.getItem('userID'); // or wherever you store the current user id

      const response = await deleteDocument(selectedDoc.documentID, userId);

      if (response.header.errorCount === 0) {
        toast.success(response.header.messages[0].messageText || 'Document deactivated successfully');
        setShowDeleteModal(false);
        setSelectedDoc(null);
        // Refresh the data
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm
        };
        const fetchRes = await fetchDocumentsByUserId(payload);
        setDocuments(fetchRes.documentMasters || []);
        setTotalRecords(fetchRes.totalRecord || 0);
      } else {
        toast.error(response.header.messages[0].messageText || 'Failed to deactivate document');
      }
    } catch (error) {
      console.error('Error deactivating document:', error);
      toast.error('Failed to deactivate document');
    } finally {
      setShowDeleteModal(false);
      setSelectedDoc(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setSelectedDoc(null);
  };

  const handleScroll = (direction) => {
    const container = tableContainerRef.current;
    if (!container) return;
    const scrollAmount = 200;
    if (direction === 'left') {
      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else if (direction === 'right') {
      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  const handleDownloadDocument = async (documentId) => {
    try {

      await downloadFileToBrowserById('document',documentId);

    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };


  const handleView = (doc) => {
    // First close any existing viewer
    setShowFileViewer(false);

    // Small delay to ensure complete unmounting
    setTimeout(() => {
      setPdfFileId(doc.documentID);
      setPdfFileType('document');
      setPdfFileExtension(doc.documentExtention);
      setViewerKey(Date.now()); // Use current timestamp as key
      setShowFileViewer(true);
    }, 100);
  };

  const handleCloseFileViewer = () => {
    setShowFileViewer(false);
    // Reset all file viewer related states
    setPdfFileId(null);
    setPdfFileType(null);
    setPdfFileExtension(null);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.documentMaster}>
          <div className={styles.panelHeader}>
            <h2>Document Registration</h2>
            <br /><br />
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
              <button className={styles.addDocBtn} onClick={handleAddDocClick}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.docTableContainer} ref={tableContainerRef}>
            <table className={styles.docTable}>
              <thead>
                <tr>
                  <th>Document Name</th>
                  <th>Document Code</th>
                  <th>Document Type</th>
                  <th>Version</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : documents.length > 0 ? (
                  documents.map((doc) => (
                    <tr key={doc.documentID}>
                      <td>{doc.documentName}</td>
                      <td>{doc.documentCode}</td>
                      <td>{doc.documentType}</td>
                      <td>{doc.documentVersion}</td>
                      <td>
                        <div
                          className={styles.tooltipWrapper}
                          onMouseEnter={handleTooltipPosition}
                        >
                          <span
                            className={`${styles.statusBadge} ${getStatusClass(doc.documentStatus)}`}
                          >
                            {doc.documentStatus}
                          </span>
                          <span className={styles.tooltipText}>
                            {doc.remark || doc.approvalRemarks || 'No remarks available'}
                          </span>
                        </div>
                      </td>
                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleView(doc)}
                            title="View Document"
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          <button
                            className={styles.downloadBtn}
                            onClick={() => handleDownloadDocument(doc.documentID, doc.documentName)}
                            title="Download Document"
                          >
                            <FaDownload className={styles.downloadIcon} />
                            <span>Download</span>
                          </button>
                          <span className={styles.actionDivider}></span>
                          <button
                            className={`${styles.editBtn} ${doc.documentStatus === 'Approved' ? styles.disabledBtn : ''}`}
                            onClick={() => handleEditDocClick(doc)}
                            title={doc.documentStatus === 'Approved' ? 'Cannot edit approved document' : 'Edit Document'}
                            disabled={doc.documentStatus === 'Approved' || doc.documentStatus === 'Rejected'}
                          >
                            <FaEdit className={`${styles.editIcon} ${doc.documentStatus === 'Approved' || doc.documentStatus === 'Rejected' ? styles.disabledIcon : ''}`} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.editBtn}
                            onClick={() => handleDeleteClick(doc)}
                            title="Deactivate Document"
                            // disabled={doc.documentStatus === 'Approved'}
                          >
                            {/* ${doc.documentStatus === 'Approved' || doc.documentStatus === 'Rejected' ? styles.disabledIcon : ''} */}
                            <FcCancel className={`${styles.deleteIcon} `} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      No documents found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className={styles.paginationContainer}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              paginate={paginate}
            />
          </div>
        </div>
      </div>
      {showDeleteModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate document "${selectedDoc?.documentName}"?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
      {showFileViewer && pdfFileId && (
        <FileViewer
          key={viewerKey}
          id={pdfFileId}
          type={pdfFileType}
          extension={pdfFileExtension}
          onClose={handleCloseFileViewer}
        />
      )}
    </>
  );
};

export default DocumentMaster;
