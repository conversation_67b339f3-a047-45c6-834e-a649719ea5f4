.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.dateInputWrapper {
  position: relative;
}

.dateInput {
  width: 100%;
  padding-right: 30px;
}

.calendarIcon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
  font-size: 18px;
  pointer-events: none;
}

.radioGroup {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
  width: fit-content;
}

.radioGroup label {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: black;
  cursor: pointer;
  margin-right: 1rem;
}

.radioGroup label input {
  margin-right: 4px;
}

.radioGroup input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #001b36;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white;
}

.radioGroup input[type="radio"]:checked::before {
  content: '✔';
  position: absolute;
  top: 2px;
  left: 2px;
  font-size: 16px;
  color: #127c96;
  font-weight: bold;
  text-align: center;
  line-height: 18px;
}

.radioGroup input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(18, 124, 150, 0.3);
}

.radioGroup input[type="radio"]:hover {
  border-color: #127c96;
}

.radioGroup input[type="radio"]:checked {
  border-color: #127c96;
}

.radioGroup input[type="radio"]:checked + label {
  color: #127c96;
}

.error {
  color: red;
  font-size: 12px;
  font-weight: 500;
}

.errorInput {
  border-color: red !important;
}

.selectDropdown {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.reactSelectWrapper {
  width: 100%;
  color: #001b36;
}

.reactSelect :global(.react-select__control) {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 2px 5px;
  font-size: 14px;
  color: black;
  min-height: 38px;
}

.reactSelect :global(.react-select__menu) {
  color: #001b36;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.reactSelect :global(.react-select__option) {
  color: #001b36;
  font-size: 14px;
  padding: 8px 12px;
}

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 2.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: #333;
  box-sizing: border-box;
}

.formContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

@media (max-width: 768px) {
  .form {
    padding: 1rem 0.8rem;
    margin: 0;
    max-width: 100%;
    border-radius: 0;
    gap: 1rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .form {
    padding: 15px;
  }

  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row input[type="radio"] {
  appearance: none;
  color: #127c96;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #127c96;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white;
  background-image: none;
}

.row input[type="radio"]:checked {
  background-color: #127c96;
  border-color: #127c96;
}

.row input[type="radio"]:checked::before {
  content: '\2713';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}

.row input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.6);
}

.inlineRow {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.inlineRow .row {
  width: 48%;
  padding-right: 10px;
}

.inlineRow .row:last-child {
  padding-right: 0;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

.required {
  color: red;
  margin-left: 4px;
}


.dateInputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.dateInputWrapper input[type="date"] {
  padding-right: 35px;
  background-color: white; /* Ensures input doesn't inherit unwanted background */
  color: #000; /* Ensures text is readable */
}

.calendarIcon {
  position: absolute;
  right: 10px;
  color: #333; /* Dark icon for visibility on white */
  pointer-events: none;
  font-size: 16px;
}

.calendarIcon {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 2px;
}



/* Modal Styles */


.actionButton {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButtonCancel {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #d0d0d0;
  color: #000000;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
  color: #222;
}

.modalContent h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.reasonTextarea {
  width: 100%;
  padding: 10px;
  border: 1.5px solid #127C96;
  border-radius: 5px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.modalButtons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.modalButtons .actionButton {
    min-width: 100px;
}

.cancelButton {
    padding: 12px 25px;
    font-size: 1rem;
    background-color: #e0e0e0;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.cancelButton:hover {
    background-color: #d5d5d5;
    border-color: #b0b0b0;
}



/* File Details Styles */
.fileDetails {
  margin-top: 0.8rem;
  padding: 0.8rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  font-size: 0.9rem;
  color: #333;
}

.fileDetails p {
  margin: 0 0 0.4rem 0;
  padding: 0;
}

.fileDetails p:last-child {
  margin-bottom: 0;
}

.fileInfoText {
  font-size: 0.85rem;
  color: #888;
  margin-top: 0.5rem; /* Adjusted margin to match RegisterDocument */
}