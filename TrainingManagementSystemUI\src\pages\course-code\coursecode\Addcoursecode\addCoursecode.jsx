import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './addCoursecode.module.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { createCourseCode, fetchApprovedDocumentsAndOjts } from '../../../../services/course-code/CoursecodeService';
import { fetchAllDepartments } from '../../../../services/systemAdmin/DepartmentMasterService';

const AddCoursecode = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const itemsPerPage = 10;
  const debounceTimeoutRef = useRef(null);

  const [formData, setFormData] = useState({
    courseCode: '',
    courseTitle: '',
    department: '',
    remark: '',
  });

  const [availableDocumentsOjts, setAvailableDocumentsOjts] = useState([]);
  const [selectedDocumentsOjts, setSelectedDocumentsOjts] = useState([]);
  const [documentOjtSearchTerm, setDocumentOjtSearchTerm] = useState('');
  const listRef = useRef(null);

  // Department state
  const [departments, setDepartments] = useState([]);
  const departmentSearchRef = useRef(null);

  const frequencyOptions = ['Monthly', 'Quarterly', 'Half Yearly', 'Yearly'];

  const [expandedDepartments, setExpandedDepartments] = useState(new Set());
  const [groupedDocuments, setGroupedDocuments] = useState({});

  // Fetch departments
  const fetchDepartments = async (searchText = '') => {
    try {
      const response = await fetchAllDepartments(1, 50, searchText);
      if (response && response.departments) {
        setDepartments(response.departments.map(dept => ({
          value: dept.departmentID,
          label: dept.departmentName
        })));
      } else {
        toast.error('Failed to fetch departments');
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.error('Error loading departments');
    }
  };

  // Initial department fetch
  useEffect(() => {
    fetchDepartments();
  }, []);

  // Department search with debounce
  useEffect(() => {
    if (departmentSearchRef.current) {
      clearTimeout(departmentSearchRef.current);
    }

    departmentSearchRef.current = setTimeout(() => {
      fetchDepartments('');
    }, 500);

    return () => {
      if (departmentSearchRef.current) {
        clearTimeout(departmentSearchRef.current);
      }
    };
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.courseCode || !formData.courseTitle || !formData.department || selectedDocumentsOjts.length === 0) {
      toast.error('Please fill in all required fields and select at least one Document/OJT.');
      return;
    }

    try {
      const userName = sessionStorage.getItem('userName') || 'system';
      const plantID = sessionStorage.getItem('plantId') || '1';

      const payload = {
        courseCodeID: 0, // 0 for creation
        courseCode: formData.courseCode,
        courseTitle: formData.courseTitle,
        departmentID: parseInt(formData.department),
        remarks: formData.remark || '',
        activityBy: userName,
        plantID: parseInt(plantID),
        reasonforChange: 'Initial Creation',
        electronicSignature: userName,
        signatureDate: new Date().toISOString(),
        courseLinkedDocuments: selectedDocumentsOjts.map(item => ({
          transactionID: 0, // 0 for creation
          documentID: item.value.startsWith('doc-') ? parseInt(item.value.replace('doc-', '')) : 0,
          ojtid: item.value.startsWith('ojt-') ? parseInt(item.value.replace('ojt-', '')) : 0,
          frequency: item.frequency || '', // Allow empty frequency
          actionType: 0 // 0 for creation
        }))
      };

      const response = await createCourseCode(payload);

      if (response?.header?.errorCount === 0) {
        toast.success(response.header.messages?.[0]?.messageText || 'Course Code created successfully!');
        setTimeout(() => {
          navigate('/course-code/course-code-registration');
        }, 1500);
      } else {
        const errorMessage = response?.header?.messages?.[0]?.messageText || 'Failed to create Course Code.';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error creating Course Code:', error);
      toast.error('An error occurred while creating Course Code.');
    }
  };

  const fetchDocuments = async (searchText = '', pageNumber = 0, append = false) => {
    try {
      setLoading(true);
      const payload = {
        page: {
          offset: pageNumber * itemsPerPage,
          fetch: itemsPerPage
        },
        searchText
      };

      const response = await fetchApprovedDocumentsAndOjts(payload);

      if (response.header.errorCount === 0) {
        const newDocuments = response.approvedDocuments.filter(
          doc => !selectedDocumentsOjts.some(
            selected => selected.value === doc.value
          )
        );

        setAvailableDocumentsOjts(prev =>
          append ? [...prev, ...newDocuments] : newDocuments
        );
        setHasMore(response.approvedDocuments.length === itemsPerPage);
      } else {
        toast.error('Failed to fetch documents');
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error('Error fetching documents');
    } finally {
      setLoading(false);
    }
  };

  const handleScroll = () => {
    if (listRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = listRef.current;
      if (scrollHeight - scrollTop <= clientHeight * 1.5 && !loading && hasMore) {
        setPage(prev => prev + 1);
      }
    }
  };

  useEffect(() => {
    fetchDocuments(documentOjtSearchTerm, 0, false);
    setPage(0);
  }, [documentOjtSearchTerm]);

  useEffect(() => {
    if (page > 0) {
      fetchDocuments(documentOjtSearchTerm, page, true);
    }
  }, [page]);

  const handleDocumentOjtSearchChange = (e) => {
    const value = e.target.value;
    setDocumentOjtSearchTerm(value);

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      fetchDocuments(value, 0, false);
    }, 500);
  };

  const handleAddItemToSelected = (itemToAdd) => {
    if (!selectedDocumentsOjts.find(item => item.value === itemToAdd.value)) {
      setSelectedDocumentsOjts([...selectedDocumentsOjts, { ...itemToAdd, frequency: '' }]);
      setAvailableDocumentsOjts(prev =>
        prev.filter(item => item.value !== itemToAdd.value)
      );
    }
  };

  const handleRemoveItemFromSelected = (itemToRemove) => {
    setSelectedDocumentsOjts(prev =>
      prev.filter(item => item.value !== itemToRemove.value)
    );
    setAvailableDocumentsOjts(prev => [...prev, itemToRemove].sort((a, b) =>
      a.label.localeCompare(b.label)
    ));
  };

  const handleFrequencyChange = (value, frequency) => {
    setSelectedDocumentsOjts(prev =>
      prev.map(item =>
        item.value === value
          ? { ...item, frequency: item.frequency === frequency ? '' : frequency }
          : item
      )
    );
  };

  useEffect(() => {
    // Group documents by department
    const grouped = availableDocumentsOjts.reduce((acc, doc) => {
      const deptName = doc.departmentName || 'Other';
      if (!acc[deptName]) {
        acc[deptName] = [];
      }
      acc[deptName].push(doc);
      return acc;
    }, {});
    setGroupedDocuments(grouped);
  }, [availableDocumentsOjts]);

  const toggleDepartment = (deptName) => {
    setExpandedDepartments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(deptName)) {
        newSet.delete(deptName);
      } else {
        newSet.add(deptName);
      }
      return newSet;
    });
  };

  return (
    <div className={styles.container}>
      <ToastContainer />
      <form className={styles.form} onSubmit={handleSubmit}>
        <h3 className={styles.sectionHeading}>Course Code Registration</h3>

        {/* Course Details Section */}
        <div className={styles.formGrid}>
          <div className={styles.row}>
            <label>Course Code <span className={styles.required}>*</span></label>
            <input
              type="text"
              name="courseCode"
              value={formData.courseCode}
              onChange={handleInputChange}
              required
              placeholder="Enter course code"
            />
          </div>

          <div className={styles.row}>
            <label>Course Title <span className={styles.required}>*</span></label>
            <input
              type="text"
              name="courseTitle"
              value={formData.courseTitle}
              onChange={handleInputChange}
              required
              placeholder="Enter course title"
            />
          </div>

          <div className={styles.row}>
            <label>Department <span className={styles.required}>*</span></label>
            <select
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Department</option>
              {departments.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>

          <div className={styles.row}>
            <label>Remarks</label>
            <textarea
              name="remark"
              value={formData.remark}
              onChange={handleInputChange}
              placeholder="Enter remarks"
            />
          </div>
        </div>

          {/* Second Container: Document and OJT Selection */}
          <div className={`${styles.formSection} ${styles.documentOjtSection}`}>
            <h3>Approved Document Selection <span className={styles.required}>*</span></h3>
            <div className={styles.documentOjtLayout}>
              {/* Left Column: Available Documents and OJTs */}
              <div className={styles.availableOjtColumn}>
                <div className={styles.documentOjtSearch}>
                  <input
                    type="text"
                    placeholder="Search Documents"
                    value={documentOjtSearchTerm}
                    onChange={handleDocumentOjtSearchChange}
                  />
                </div>
                <div
                  className={styles.documentOjtList}
                  ref={listRef}
                  onScroll={handleScroll}
                >
                  {Object.keys(groupedDocuments).length > 0 ? (
                    Object.entries(groupedDocuments).map(([deptName, docs]) => (
                      <div key={deptName} className={styles.departmentGroup}>
                        <button
                          type="button"
                          className={styles.departmentHeader}
                          onClick={() => toggleDepartment(deptName)}
                        >
                          <span className={styles.expandIcon}>
                            {expandedDepartments.has(deptName) ? '🔽' : '🔼'}
                          </span>
                          {deptName} ({docs.length})
                        </button>
                        {expandedDepartments.has(deptName) && (
                          <div className={styles.departmentDocs}>
                            {docs.map(item => (
                              <div key={item.value} className={styles.documentOjtItem}>
                                <div className={styles.documentDetails}>
                                  <div className={styles.documentMeta}>
                                    ({item.type || 'N/A'}) [{item.description || 'N/A'}]
                                  </div>
                                  <div className={styles.documentName}>{item.label || 'Untitled'}</div>
                                </div>
                                <button
                                  type="button"
                                  className={styles.addItemButton}
                                  onClick={() => handleAddItemToSelected(item)}
                                >
                                  +
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className={styles.noResults}>
                      {loading ? 'Loading...' : 'No matching Documents or OJTs available.'}
                    </div>
                  )}
                  {loading && (
                    <div className={styles.loadingIndicator}>Loading more...</div>
                  )}
                </div>
              </div>

              {/* Divider */}
              <div className={styles.layoutDivider}></div>

              {/* Right Column: Selected Documents and OJTs */}
              <div className={styles.selectedOjtColumn}>
                <div className={styles.selectedItemsContainerheadings}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%'}}>
                    <h4 style={{ margin: 0 }}>Selected Items</h4>
                    <h4 style={{ margin: 0 }}>Recurring Training Frequency</h4>
                  </div>
                </div>
                {selectedDocumentsOjts.length > 0 ? (
                  <div className={styles.selectedItemsContainer}>
                    {selectedDocumentsOjts.map(item => (
                      <div key={item.value} className={styles.selectedItemBubble}>
                        <span className={styles.itemLabel}>{item.label}</span>
                        <div className={styles.frequencyOptions}>
                          {frequencyOptions.map(frequency => (
                            <button
                              key={frequency}
                              type="button"
                              className={`${styles.frequencyToggle} ${item.frequency === frequency ? styles.selected : ''}`}
                              onClick={() => handleFrequencyChange(item.value, frequency)}
                            >
                              {frequency}
                            </button>
                          ))}
                        </div>
                        <button
                          type="button"
                          className={styles.removeItemButton}
                          onClick={() => handleRemoveItemFromSelected(item)}
                        >
                          -
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className={styles.noSelected}>No Documents or OJTs selected yet.</div>
                )}
              </div>
            </div>
          </div>

        <div className={styles.submitRow}>
          <button type="submit" className={styles.primaryBtn}>Submit</button>
          <button type="button" className={styles.cancelBtn} onClick={() => navigate('/course-code/course-code-registration')}>Cancel</button>
        </div>
      </form>
    </div>
  );
};

export default AddCoursecode;
