import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/pagination/Pagination';
import Modal from '../../../components/common/Modal';
import styles from './QuestionApprove.module.css';
import { fetchPendingQuestions, updateQuestionStatus, getQuestionsByPreparationId } from '../../../services/sopojt-management/QuestionApproveService';
import { FaCheck, FaTimes, FaUndo, FaEye } from 'react-icons/fa';

const QuestionApprove = () => {
  const navigate = useNavigate();
  const [questions, setQuestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [questionDetails, setQuestionDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [rejectReason, setRejectReason] = useState('');
  const [approveReason, setApproveReason] = useState('');
  const [returnReason, setReturnReason] = useState('');

  // Debounced search effect (500ms delay)
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const loadQuestions = async () => {
      try {
        setLoading(true);
        const data = await fetchPendingQuestions();
        console.log('Pending Questions Data:', data);

        const message = data.header?.messages?.[0];
        if (message?.messageLevel?.toLowerCase() === 'warning') {
          toast.warning(message.messageText);
        } else if (message?.messageLevel?.toLowerCase() === 'error') {
          toast.error(message.messageText);
        }

        if (data.header?.errorCount === 0 && Array.isArray(data.questions)) {
          setQuestions(data.questions);

          setTotalRecords(data.totalRecord || data.questions.length);
        } else {
          console.error('Failed to load pending questions:', data.header?.message);
          toast.error('Failed to load pending questions');
           setQuestions([]);
           setTotalRecords(0);
        }
      } catch (error) {
        toast.error('Error fetching pending questions');
        console.error('Error fetching pending questions:', error);
         setQuestions([]);
         setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };

    loadQuestions();
  }, [refreshKey]);

  // Filter questions based on debounced search term (client-side filtering)
  const filteredQuestions = questions.filter(question =>
    Object.values(question).some(value =>
      String(value).toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    )
  );

  // Pagination logic (client-side pagination after filtering)
  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);
  console.log(filteredQuestions.length);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentQuestions = filteredQuestions.slice(startIndex, endIndex);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleApproveClick = (question) => {
    setSelectedQuestion(question);
    setShowApproveModal(true);
  };

  const handleRejectClick = (question) => {
    setSelectedQuestion(question);
    setShowRejectModal(true);
  };

  const handleReturnClick = (question) => {
    setSelectedQuestion(question);
    setShowReturnModal(true);
  };

  const handleViewClick = async (question) => {
    setSelectedQuestion(question);
    setShowViewModal(true);
    setLoadingDetails(true);
    try {
      const data = await getQuestionsByPreparationId(question.preparationID);
      setQuestionDetails(data.questions);
    } catch (error) {
      toast.error('Failed to load question details');
      console.error('Error loading question details:', error);
    } finally {
      setLoadingDetails(false);
    }
  };

  const confirmApprove = async () => {
    if (!approveReason.trim()) {
      toast.warning('Please provide a reason for approval');
      return;
    }

    try {
      const response = await updateQuestionStatus({
        preparationID: selectedQuestion.preparationID,
        questionStatus: "Approved",
        modifiedBy: localStorage.getItem('userId') || '2',
        reasonForChange: approveReason
      });

      if (response.header.errorCount === 0) {
        toast.success('Question approved successfully');
        setShowApproveModal(false);
        setApproveReason('');
        setRefreshKey(prev => prev + 1);
      } else {
        toast.error(response.header.messages[0]?.messageText || 'Failed to approve question');
      }
    } catch (error) {
      toast.error(error.message || 'Error approving question');
      console.error('Error approving question:', error);
    }
  };

  const confirmReject = async () => {
    if (!rejectReason.trim()) {
      toast.warning('Please provide a reason for rejection');
      return;
    }

    try {
      const response = await updateQuestionStatus({
        preparationID: selectedQuestion.preparationID,
        questionStatus: "Rejected",
        modifiedBy: localStorage.getItem('userId') || '2',
        reasonForChange: rejectReason
      });

      if (response.header.errorCount === 0) {
        toast.success('Question rejected successfully');
        setShowRejectModal(false);
        setRejectReason('');
        setRefreshKey(prev => prev + 1);
      } else {
        toast.error(response.header.messages[0]?.messageText || 'Failed to reject question');
      }
    } catch (error) {
      toast.error(error.message || 'Error rejecting question');
      console.error('Error rejecting question:', error);
    }
  };

  const confirmReturn = async () => {
    if (!returnReason.trim()) {
      toast.warning('Please provide a reason for return');
      return;
    }

    try {
      const response = await updateQuestionStatus({
        preparationID: selectedQuestion.preparationID,
        questionStatus: "Returned",
        modifiedBy: localStorage.getItem('userId') || '2',
        reasonForChange: returnReason
      });

      if (response.header.errorCount === 0) {
        toast.success('Question returned successfully');
        setShowReturnModal(false);
        setReturnReason('');
        setRefreshKey(prev => prev + 1);
      } else {
        toast.error(response.header.messages[0]?.messageText || 'Failed to return question');
      }
    } catch (error) {
      toast.error(error.message || 'Error returning question');
      console.error('Error returning question:', error);
    }
  };

  const cancelAction = () => {
    setShowApproveModal(false);
    setShowRejectModal(false);
    setShowReturnModal(false);
    setShowViewModal(false);
    setSelectedQuestion(null);
    setQuestionDetails(null);
    setRejectReason('');
    setApproveReason('');
    setReturnReason('');
  };

  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const getStatusClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'draft':
        return styles.statusDraft;
      case 'approved':
        return styles.statusApproved;
      case 'returned':
        return styles.statusReturn;
      case 'rejected':
        return styles.statusReject;
      default:
        return styles.statusDraft;
    }
  };

  return (
    <>
      <ToastContainer />
      {/* View Modal */}
      {showViewModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.viewModal}>
            <div className={styles.modalHeader}>
              <h2>CSK - AMT</h2>
              <button
                className={styles.closeButton}
                onClick={() => setShowViewModal(false)}
              >
                ×
              </button>
            </div>
            <div className={styles.viewModalContent}>
              {loadingDetails ? (
                <div className={styles.loadingSpinner} />
              ) : questionDetails && questionDetails.length > 0 ? (
                <div className={styles.questionsList}>
                  {questionDetails.map((question, index) => (
                    <div key={question.questionID} className={styles.questionItem}>
                      <div className={styles.questionHeader}>
                        <span className={styles.questionNumber}>Q{index + 1}.</span>
                        <span className={styles.questionText}>{question.questionText}</span>
                        <span className={styles.questionMarks}>({question.marks} marks)</span>
                        {question.isMandatory && <span className={styles.mandatoryBadge}>Mandatory</span>}
                      </div>
                      <div className={styles.optionsList}>
                        {question.options.map((option) => (
                          <div
                            key={option.optionID}
                            className={`${styles.optionItem} ${option.isCorrect ? styles.correctOption : ''}`}
                          >
                            <span className={styles.optionText}>{option.optionText}</span>
                            {option.isCorrect && <span className={styles.correctBadge}>Correct Answer</span>}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={styles.noQuestions}>No questions found for this document.</div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Approve Modal */}
      {showApproveModal && (
        <Modal
          title="Confirm Approval"
          message={
            <div>
              <p>Are you sure you want to approve the questions for document "{selectedQuestion?.documentName}"?</p>
              <div className={styles.reasonInput}>
                <label htmlFor="approveReason">Reason for approval:</label>
                <textarea
                  id="approveReason"
                  value={approveReason}
                  onChange={(e) => setApproveReason(e.target.value)}
                  placeholder="Please provide a reason for approval"
                  required
                />
              </div>
            </div>
          }
          onConfirm={confirmApprove}
          onCancel={cancelAction}
        />
      )}

      {/* Return Modal */}
      {showReturnModal && (
        <Modal
          title="Confirm Return"
          message={
            <div>
              <p>Are you sure you want to return the questions for document "{selectedQuestion?.documentName}"?</p>
              <div className={styles.reasonInput}>
                <label htmlFor="returnReason">Reason for return:</label>
                <textarea
                  id="returnReason"
                  value={returnReason}
                  onChange={(e) => setReturnReason(e.target.value)}
                  placeholder="Please provide a reason for return"
                  required
                />
              </div>
            </div>
          }
          onConfirm={confirmReturn}
          onCancel={cancelAction}
        />
      )}
      {/* Reject Modal */}
      {showRejectModal && (
        <Modal
          title="Confirm Rejection"
          message={
            <div>
              <p>Are you sure you want to reject the questions for document "{selectedQuestion?.documentName}"?</p>
              <div className={styles.reasonInput}>
                <label htmlFor="rejectReason">Reason for rejection:</label>
                <textarea
                  id="rejectReason"
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  placeholder="Please provide a reason for rejection"
                  required
                />
              </div>
            </div>
          }
          onConfirm={confirmReject}
          onCancel={cancelAction}
        />
      )}

      <div className={styles.container}>
        <div className={styles.questionApproveMaster}>
          <div className={styles.panelHeader}>
            <h2>Questioner Approval</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
            </div>
          </div>

          <div className={styles.userTableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th>Document Name</th>
                  <th>Document Code</th>
                  <th>Required Questions</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="4" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : currentQuestions.length > 0 ? (
                  currentQuestions.map((question, index) => (
                    <tr key={question.preparationID || index}>
                      <td>{question.documentName}</td>
                      <td>{question.documentCode}</td>
                      <td>{question.requiredQuestions}</td>

                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleViewClick(question)}
                            title="View Questions"
                          >
                            <FaEye className={styles.viewIcon} />
                            <span>View</span>
                          </button>
                          <span className={styles.actionDivider}></span>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleApproveClick(question)}
                            title="Approve Questions"
                          >
                            <FaCheck className={styles.approveIcon} />
                            <span>Approve</span>
                          </button>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleReturnClick(question)}
                            title="Return Questions"
                          >
                            <FaUndo className={styles.returnIcon} />
                            <span>Return</span>
                          </button>
                          <button
                            className={styles.actionButton}
                            onClick={() => handleRejectClick(question)}
                            title="Reject Questions"
                          >
                            <FaTimes className={styles.rejectIcon} />
                            <span>Reject</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                      No questions pending approval.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default QuestionApprove;