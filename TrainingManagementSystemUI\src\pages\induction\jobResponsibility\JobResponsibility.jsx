import React, { useState, useEffect, useContext, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './JobResposibility.module.css';
import { FaEdit } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import { fetchAllJobResponsibilities, deleteJobResponsibility } from '../../../services/induction/JobResponsibilityService';
import Pagination from '../../../components/pagination/Pagination';
import { JobResponsibilityContext } from '../../../context/induction/JobResponsibilityContext';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';

const JobResponsibility = () => {
  const navigate = useNavigate();
  const { setJobResponsibilityId, setJobResponsibilityDetails } = useContext(JobResponsibilityContext);
  const tableContainerRef = useRef(null);

  const [jobResponsibilities, setJobResponsibilities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [selectedJobResponsibility, setSelectedJobResponsibility] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const itemsPerPage = 10;

  useEffect(() => {
    loadJobResponsibilities();
  }, [currentPage]);

  const loadJobResponsibilities = async () => {
    try {
      const response = await fetchAllJobResponsibilities(currentPage, itemsPerPage);
      if (response.jobResponsibilities) {
        setJobResponsibilities(response.jobResponsibilities);
        setTotalRecords(response.totalRecord || 0);
      }
      setLoading(false);
    } catch {
      toast.error('Failed to load job responsibilities');
      setLoading(false);
    }
  };

  const handleEditJobResponsibility = (jobResponsibility) => {
    try {
      if (!jobResponsibility || !jobResponsibility.jobResponsibilityID) {
        toast.error('Invalid job responsibility data');
        return;
      }

      setJobResponsibilityId(jobResponsibility.jobResponsibilityID);
      setJobResponsibilityDetails(jobResponsibility);

      const jobResponsibilityDataToStore = {
        jobResponsibilityID: jobResponsibility.jobResponsibilityID,
        userID: jobResponsibility.userID,
        inductionID: jobResponsibility.inductionID,
        jobTitle: jobResponsibility.jobTitle,
        title: jobResponsibility.title,
        description: jobResponsibility.description,
        responsibilities: jobResponsibility.responsibilities,
        firstName: jobResponsibility.firstName,
        lastName: jobResponsibility.lastName,
        plantName: jobResponsibility.plantName,
        userName: `${jobResponsibility.firstName} ${jobResponsibility.lastName}`
      };

      localStorage.setItem('editJobResponsibilityFormData', JSON.stringify(jobResponsibilityDataToStore));
      navigate('/induction/job-responsibility/edit-job-responsibility');
    } catch (error) {
      console.error('Error preparing job responsibility data for edit:', error);
      toast.error('Failed to prepare job responsibility data for editing');
    }
  };

  const handleDeactivateClick = (jobResponsibility) => {
    setSelectedJobResponsibility(jobResponsibility);
    setShowDeactivateModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedJobResponsibility) return;
    setLoading(true);
    try {
      const response = await deleteJobResponsibility(selectedJobResponsibility.jobResponsibilityID);
      
      if (response.header?.errorCount === 0) {
        const successMsg = response.header?.messages?.[0]?.messageText || 'Job responsibility deactivated successfully';
        toast.success(successMsg);
        loadJobResponsibilities();
      } else {
        const errorMsg = response.header?.messages?.[0]?.messageText || 'Failed to deactivate job responsibility';
        toast.error(errorMsg);
      }
    } catch (error) {
      console.error('Error deactivating job responsibility:', error);
      toast.error('Failed to deactivate job responsibility. Please try again.');
    } finally {
      setLoading(false);
      setShowDeactivateModal(false);
      setSelectedJobResponsibility(null);
    }
  };

  const cancelDelete = () => {
    setShowDeactivateModal(false);
    setSelectedJobResponsibility(null);
  };

  const handleAddJobResponsibility = () => {
    navigate('/induction/job-responsibility/add-job-responsibility');
  };

  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      {showDeactivateModal && (
        <Modal
          title="Confirm Deactivate"
          message={`Are you sure you want to deactivate the job responsibility for ${selectedJobResponsibility?.firstName} ${selectedJobResponsibility?.lastName}?`}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />
      )}
      
      <div className={styles.container}>
        <div className={styles.JobResposibility}>
          <div className={styles.panelHeader}>
            <h2>Job Responsibility</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
              <button className={styles.addUserBtn} onClick={handleAddJobResponsibility}>
                + Add
              </button>
            </div>
          </div>

          <div className={styles.jobTableContainer} ref={tableContainerRef}>
            <table className={styles.jobTable}>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Plant Name</th>
                  <th>Title</th>
                  <th>Description</th>
                  <th>Responsibilities</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="6" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : jobResponsibilities.length === 0 ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: "center" }}>
                      No job responsibilities found.
                    </td>
                  </tr>
                ) : (
                  jobResponsibilities.map((item, index) => (
                    <tr key={`${item.jobResponsibilityID}-${item.userID}-${index}`}>
                      <td>{`${item.firstName} ${item.lastName}`}</td>
                      <td>{item.plantName || '-'}</td>
                      <td>{item.title}</td>
                      <td>{item.description}</td>
                      <td>{item.responsibilities}</td>
                      <td>
                        <div className={styles.actions}>
                          <button
                            className={styles.editBtn}
                            onClick={() => handleEditJobResponsibility(item)}
                            title="Edit Job Responsibility"
                          >
                            <FaEdit className={styles.editIcon} />
                            <span>Edit</span>
                          </button>
                          <button
                            className={styles.deactivateBtn}
                            onClick={() => handleDeactivateClick(item)}
                            title="Deactivate Job Responsibility"
                          >
                            <FcCancel className={styles.deleteIcon} />
                            <span>Disable</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default JobResponsibility;