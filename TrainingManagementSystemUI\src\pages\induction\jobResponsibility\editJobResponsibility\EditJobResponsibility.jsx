import React, { useState, useEffect, useCallback, useRef } from 'react';
import styles from './EditJobResponsibility.module.css';

import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchUsersNotAssignedJobResponsibility, updateJobResponsibility, fetchJobResponsibilityLookupByUserId } from '../../../../services/induction/JobResponsibilityService';
import Modal from '../../../../components/common/Modal';

const TOAST_CONFIG = {
  position: "top-right",
  autoClose: 3000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  toastId: 'unique-toast'
};

const EditJobResponsibility = () => {
  const navigate = useNavigate();
  const hasFetched = useRef(false);
  const initialFormDataRef = useRef(null);

  const [formData, setFormData] = useState({
    userID: '',
    inductionID: '',
    title: '',
    JobResposibility: '',
    responsibilities: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [titleInputValue, setTitleInputValue] = useState('');
  const [filteredJobTitles, setFilteredJobTitles] = useState([]);
  const [isTitleMenuOpen, setIsTitleMenuOpen] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');

  const fetchData = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetchUsersNotAssignedJobResponsibility();
      console.log('Fetched user data:', response);

      if (response.header?.errorCount > 0) {
        toast.error(response.header.messages[0].messageText, TOAST_CONFIG);
        return;
      }

      if (response.users) {
        const formattedUsers = response.users.map((u) => ({
          value: u.userID.toString(),
          label: u.userName,
          inductionID: u.inductionID
        }));
        console.log('Formatted users:', formattedUsers);
      }
    } catch (error) {
      const msg =
        error.response?.data?.header?.messages?.[0]?.messageText ||
        'Error fetching users. Please try again.';
      toast.error(msg, TOAST_CONFIG);
    } finally {
      setIsLoading(false);
      }
  }, [isLoading]);

  useEffect(() => {
    if (!hasFetched.current) {
      fetchData();
      hasFetched.current = true;
    }
  }, [fetchData]);

  useEffect(() => {
    const loadJobResponsibilityData = () => {
      try {
        const storedData = localStorage.getItem('editJobResponsibilityFormData');
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          console.log('Loaded stored data:', parsedData);

          const initialData = {
            userID: parsedData.userID || '',
            inductionID: parsedData.inductionID || '',
            title: parsedData.title || '',
            JobResposibility: parsedData.description || '',
            responsibilities: parsedData.responsibilities || ''
          };

          setFormData(initialData);
          initialFormDataRef.current = { ...initialData };

          if (parsedData.userID && parsedData.firstName && parsedData.lastName) {
            const user = {
              value: parsedData.userID.toString(),
              label: `${parsedData.firstName} ${parsedData.lastName}`,
              inductionID: parsedData.inductionID
            };
            setSelectedUser(user);
          }

          setTitleInputValue(parsedData.title || '');
          if (parsedData.title) {
            const filtered = JOB_TITLES.filter(option =>
              option.label.toLowerCase().includes(parsedData.title.toLowerCase())
            );
            setFilteredJobTitles(filtered);
          }

          return;
        }

        toast.error('No job responsibility data found for editing', TOAST_CONFIG);
        navigate('/induction/job-Responsibility');
      } catch (error) {
        console.error('Error loading job responsibility data:', error);
        toast.error('Failed to load job responsibility data', TOAST_CONFIG);
        navigate('/induction/job-Responsibility');
      }
    };

    loadJobResponsibilityData();
  }, [navigate]);

  useEffect(() => {
    const fetchJobLookup = async () => {
      const userId = sessionStorage.getItem('userId');
      try {
        const response = await fetchJobResponsibilityLookupByUserId(userId);
        
        if (response.header?.errorCount > 0) {
          const errorMsg = response.header.messages[0]?.messageText || 'Failed to fetch job responsibility lookup';
          toast.error(errorMsg, TOAST_CONFIG);
          return;
        }

        if (response.jobResponsibiltyLookup) {
          setJobLookupData(response.jobResponsibiltyLookup);
          // Convert jobLookupData to Select options format without filtering
          const options = response.jobResponsibiltyLookup.map(job => ({
            value: job.title,
            label: job.title,
            description: job.description,
            responsibilities: job.responsibilities
          }));
          setTitleOptions(options);
          console.log('Title options:', options); // Add this to debug
        }
      } catch (error) {
        const errorMsg = error.response?.data?.header?.messages?.[0]?.messageText || 'Error fetching job responsibility lookup';
        toast.error(errorMsg, TOAST_CONFIG);
      }
    };

    fetchJobLookup();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (selected, field) => {
    console.log('Selected value:', selected);
    if (field === 'userID') {
      setSelectedUser(selected);
      setFormData((prev) => ({
        ...prev,
        userID: selected ? selected.value : '',
        inductionID: selected ? selected.inductionID : ''
      }));
    } else if (field === 'title') {
      if (selected) {
        const selectedJob = jobLookupData.find(job => job.title === selected.value);
        if (selectedJob) {
          setFormData(prev => ({
            ...prev,
            title: selectedJob.title,
            JobResposibility: selectedJob.description,
            responsibilities: selectedJob.responsibilities
          }));
        }
      } else {
        setFormData(prev => ({
          ...prev,
          title: '',
          JobResposibility: '',
          responsibilities: ''
        }));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const requiredFields = ['userID', 'title', 'JobResposibility'];
    for (let field of requiredFields) {
      if (!formData[field]) {
        toast.error(`Please fill in the ${field} field.`, TOAST_CONFIG);
        return;
      }
    }

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.', TOAST_CONFIG);
      return;
    }

    setIsSubmitting(true);

    try {
      const storedData = JSON.parse(localStorage.getItem('editJobResponsibilityFormData'));
      if (!storedData) {
        throw new Error('No stored job responsibility data found');
      }

      const userData = JSON.parse(sessionStorage.getItem('userData') || '{}');

      const payload = {
        jobResponsibilityID: parseInt(storedData.jobResponsibilityID),
        inductionID: parseInt(storedData.inductionID),
        userID: parseInt(storedData.userID),
        jobDescriptionMasterID: 1,
        title: formData.title,
        description: formData.JobResposibility,
        responsibilities: formData.responsibilities || '',
        modifiedBy: userData.userID?.toString() || '2',
        reasonForChange: reasonForChange,
        electronicSignature: userData.firstName + ' ' + userData.lastName,
        signatureDate: new Date().toISOString()
      };

      console.log('Submitting payload:', payload);
      const response = await updateJobResponsibility(payload);
      
      if (response.header?.errorCount > 0) {
        const errorMessage = response.header.messages[0]?.messageText || 'Failed to update job responsibility';
        toast.error(errorMessage, TOAST_CONFIG);
        return;
      }

      if (response.header?.informationCount > 0) {
        const successMessage = response.header.messages[0]?.messageText || 'Job responsibility updated successfully';
        toast.success(successMessage, TOAST_CONFIG);
        localStorage.removeItem('editJobResponsibilityFormData');
        
        setTimeout(() => {
          navigate('/induction/job-Responsibility');
        }, 3000);
      }
    } catch (error) {
      console.error('Error updating job responsibility:', error);
      const errorMessage = error.response?.data?.header?.messages?.[0]?.messageText || 
        'An error occurred while updating job responsibility';
      toast.error(errorMessage, TOAST_CONFIG);
    } finally {
      setIsSubmitting(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        limit={1}
      />
      
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Edit Job Responsibility</h3>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                User <span className={styles.required}>*</span>
              </label>
              <input
                type="text"
                value={selectedUser ? selectedUser.label : ''}
                readOnly
                className={styles.readOnlyInput}
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                Title <span className={styles.required}>*</span>
              </label>
              <Select
                name="title"
                options={titleOptions}
                value={titleOptions.find(opt => opt.value === formData.title) || null}
                onChange={(selected) => handleSelectChange(selected, 'title')}
                isClearable
                isSearchable
                placeholder="-- Select Title --"
                className={styles.reactSelect}
                classNamePrefix="select"
                isDisabled={!jobLookupData}
                noOptionsMessage={() => "No titles available"}
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                Description <span className={styles.required}>*</span>
              </label>
              <textarea
                name="JobResposibility"
                value={formData.JobResposibility}
                onChange={handleChange}
                rows="2"
                required
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>Responsibilities (Optional)</label>
              <textarea
                name="responsibilities"
                value={formData.responsibilities}
                onChange={handleChange}
                rows="2"
              />
            </div>

            <div className={styles.buttonContainer}>
              <button 
                type="submit" 
                className={styles.primaryBtn}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating...' : 'Update'}
              </button>
              <button 
                type="button" 
                className={styles.cancelBtn} 
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the Job Responsibility for "{selectedUser?.label}"</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </>
  );
};

export default EditJobResponsibility;