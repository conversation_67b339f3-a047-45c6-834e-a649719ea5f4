import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './EditDepartment.module.css';

import { useNavigate, useLocation } from 'react-router-dom';
import { DepartmentContext } from '../../../../context/DepartmentContext';
import { updateDepartment } from '../../../../services/systemAdmin/DepartmentMasterService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import Modal from '../../../../components/common/Modal';

const EditDepartment = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedDepartment } = useContext(DepartmentContext);
  const initialFormDataRef = useRef(null);

  const [loading, setLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');

  const [formData, setFormData] = useState({
    departmentID: selectedDepartment?.departmentId || '',
    departmentName: selectedDepartment?.departmentName || '',
  });

  useEffect(() => {
    if (location.state?.departmentData) {
      const initialData = {
        departmentID: location.state.departmentData.departmentID,
        departmentName: location.state.departmentData.departmentName,
      };
      setFormData(initialData);
      initialFormDataRef.current = { ...initialData };
    }
  }, [location.state]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }

    setLoading(true);
  
    try {
      const departmentData = {
        departmentID: formData.departmentID,
        departmentName: formData.departmentName.trim(),
        modifiedBy: "Admin",
        plantID: 1,
        reasonForChange: reasonForChange,
        electronicSignature: "Admin",
        signatureDate: new Date().toISOString().split('T')[0],
      };
  
      const response = await updateDepartment(departmentData);
  
      if (response.header?.errorCount === 0) {
        const infoMsg = response.header?.messages?.[0]?.messageText;
        toast.success(infoMsg); // Display success message
        setTimeout(() => {
          navigate('/system-admin/department-master');
        }, 1200);
      } else {
        if (response.header?.errorCount !== 0) {
          const errorMsg = response.header?.messages?.[0]?.messageText;
          if (errorMsg) {
            toast.error(errorMsg);
          }
        }
      }
    } catch (error) {
      console.error('Error updating department:', error);
      toast.error("An error occurred while updating the department");
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  return (
    <>
      <ToastContainer 
              position="top-right"
              autoClose={1500}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              pauseOnFocusLoss
              draggable
              pauseOnHover
            />
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          {loading && (
            <div className="progress mb-3" style={{ height: '3px' }}>
              <div
                className="progress-bar progress-bar-striped progress-bar-animated"
                role="progressbar"
                style={{ width: '100%' }}
              />
            </div>
          )}

          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Edit Department</h3>

            <div className={styles.row}>
              <label>
                Department Name <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                name="departmentName"
                value={formData.departmentName}
                onChange={handleChange}
                placeholder="Enter Department Name"
                required
              />
            </div>
          </div>

          <div className={styles.submitRow}>
            <button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update'}
            </button>
            <button type="button" onClick={() => navigate(-1)}>Cancel</button>
          </div>
        </form>
      </div>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the department "{formData.departmentName}"</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </>
  );
};

export default EditDepartment;