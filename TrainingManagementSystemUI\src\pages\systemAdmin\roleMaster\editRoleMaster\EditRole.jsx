import React, { useState, useEffect, useContext, useRef } from "react";
import styles from "./EditRole.module.css";
import Sidebar from "../../../../components/Sidebar/Sidebar";
import { useNavigate, useLocation } from "react-router-dom";
import { RoleContext } from "../../../../context/RoleContext";
import { updateRole } from "../../../../services/systemAdmin/RoleMasterService";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Modal from "../../../../components/common/Modal";

const EditRole = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedRole } = useContext(RoleContext);
  const initialFormDataRef = useRef(null);

  const [formData, setFormData] = useState({
    roleID: selectedRole?.roleID || "",
    roleName: selectedRole?.roleName || "",
    description: selectedRole?.description || "",
  });

  const [loading, setLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState("");

  useEffect(() => {
    if (location.state?.roleData) {
      const initialData = {
        roleID: location.state.roleData.roleID,
        roleName: location.state.roleData.roleName,
        description: location.state.roleData.description || "",
      };
      setFormData(initialData);
      initialFormDataRef.current = { ...initialData };
    }
  }, [location.state]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error("Please provide a reason for the change.");
      return;
    }

    const userId = localStorage.getItem("userId") || "11"; // fallback user ID
    const payload = {
      roleID: formData.roleID,
      roleName: formData.roleName,
      description: formData.description,
      modifiedBy: userId,
      plantID: 0,
      reasonForChange: reasonForChange,
      electronicSignature: formData.roleName,
      signatureDate: new Date().toISOString(),
    };

    setLoading(true);
    try {
      const response = await updateRole(payload);

      if (response.header?.errorCount === 0) {
        const infoMsg = response.header?.messages?.find(msg => msg.messageLevel === 'Information')?.messageText;
        toast.success(infoMsg || "Role updated successfully.");
        setTimeout(() => {
          navigate("/system-admin/role-master");
        }, 3000);
      } else {
        toast.error(response.header?.message || "Failed to update role.");
      }
    } catch (error) {
      console.error("Error updating role:", error);
      toast.error("An error occurred while updating the role.");
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange("");
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange("");
  };

  return (
    <>
      <div className={styles.container}>
        <ToastContainer 
          position="top-right"
          autoClose={1500}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Edit Role</h3>
            {loading && <div className={styles.spinner}></div>}
            <div className={styles.row}>
              <label htmlFor="roleName">
                Role Name <span className={styles.required}>*</span>
              </label>
              <input
                type="text"
                name="roleName"
                value={formData.roleName}
                onChange={handleChange}
                placeholder="Enter role name"
                required
              />
            </div>
            <div className={styles.row}>
              <label htmlFor="description">Description <span className={styles.required}>*</span></label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Enter description (optional)"
              ></textarea>
            </div>
          </div>

          <div className={styles.submitRow}>
            <button type="submit" disabled={loading}>
              {loading ? "Updating..." : "Update"}
            </button>
            <button type="button" onClick={() => navigate(-1)}>
              Cancel
            </button>
          </div>
        </form>

        {/* Reason for Change Modal */}
        {showReasonModal && (
          <Modal
            title="Reason for Change"
            message={
              <div>
                <p>Please provide a reason for updating the role "{formData.roleName}"</p>
                <div className={styles.reasonInput}>
                  <br />
                  <textarea
                    value={reasonForChange}
                    onChange={(e) => setReasonForChange(e.target.value)}
                    placeholder="Please provide a reason for this change..."
                    required
                  />
                </div>
              </div>
            }
            onConfirm={handleConfirmUpdate}
            onCancel={handleCancelUpdate}
          />
        )}
      </div>
    </>
  );
};

export default EditRole;
