import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './EditDesignation.module.css';

import { useNavigate, useLocation } from 'react-router-dom';
import { DesignationContext } from '../../../..//context/DeignationContext'; 
import { updateDesignation } from '../../../../services/systemAdmin/DesignationMasterService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'bootstrap/dist/css/bootstrap.min.css'; 
import Modal from '../../../../components/common/Modal';

const EditDesignation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedDesignation } = useContext(DesignationContext);
  const initialFormDataRef = useRef(null);

  const [loading, setLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');

  const [formData, setFormData] = useState({
    DesignationID: selectedDesignation.designationId || '',
    DesignationName: selectedDesignation.designationName || '',
  });

  useEffect(() => {
    if (location.state?.designationData) {
      const initialData = {
        DesignationID: location.state.designationData.designationID,
        DesignationName: location.state.designationData.designationName,
      };
      setFormData(initialData);
      initialFormDataRef.current = { ...initialData };
    }
  }, [location.state]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }

    setLoading(true);
  
    const requestBody = {
      designationID: formData.DesignationID,
      designationName: formData.DesignationName.trim(),
      modifiedBy: "Your Name or User ID", // Replace with actual user ID or name
      plantID: 0,
      reasonForChange: reasonForChange,
      electronicSignature: "Your Electronic Signature", // Replace appropriately
      signatureDate: new Date().toISOString()
    };
  
    try {
      const response = await updateDesignation(requestBody);
      const message = response?.header?.messages?.[0];
  
      if (message?.messageLevel) {
        const level = message.messageLevel.toLowerCase();
        if (level === 'error') toast.error(message.messageText);
        else if (level === 'warning') toast.warning(message.messageText);
        else if (level === 'information') {
          toast.success(message.messageText);
          setTimeout(() => {
            navigate('/system-admin/designation-master');
          }, 3000);
        }
      } else {
        toast.error('Unexpected response from server.');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('An unexpected error occurred while updating.');
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  return (
    <>
      <ToastContainer />
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formContent}>
            {loading && (
              <div className="progress mb-3" style={{ height: '3px' }}>
                <div
                  className="progress-bar progress-bar-striped progress-bar-animated"
                  role="progressbar"
                  style={{ width: '100%' }}
                />
              </div>
            )}
            <h3 className={styles.sectionHeading}>Edit Designation</h3>
            <div className={styles.row}>
              <label>
                Designation Name <span style={{ color: 'red' }}>*</span>
              </label>
              <input
                type="text"
                name="DesignationName"
                value={formData.DesignationName}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className={styles.submitRow}>
            <button type="submit" disabled={loading}>
              {loading ? 'Updating...' : 'Update'}
            </button>
            <button type="button" onClick={() => navigate(-1)}>Cancel</button>
          </div>
        </form>
      </div>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the designation "{formData.DesignationName}"</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </>
  );
};

export default EditDesignation;