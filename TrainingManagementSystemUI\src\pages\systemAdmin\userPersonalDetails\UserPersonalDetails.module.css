.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.userMaster {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

  .panelHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .panelHeader h2 {
    font-size: 24px;
    font-weight: 600;
    color: black;
  }

  .controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .controls input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 13px;
    width: 200px;
  }

  .filterBtn, .addUserBtn {
    background-color: #137688;
    color: white;
    padding: 8px 14px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 400;
    cursor: pointer;
  }

  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Ensure the modal is on top */
  }

  .modalContent {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box; /* Ensure padding doesn't overflow */
    color: #2c3e50;
  }

  .modalActions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .confirmBtn, .cancelBtn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
  }

  .confirmBtn {
    background-color: #127C96    ;
    color: white;
  }

  .cancelBtn {
    background-color: #b8b8b8;
    color: white;
  }

  .userTableContainer {
    margin-top: 20px;
    flex-grow: 1;
    height: 70%;
    overflow: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: #127C96 #f1f1f1;
    padding-bottom: 20px;
  }

  .userTable {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }

  .userTable th {
    position: sticky;
    top: 0;
    text-align: left;
    background-color: white;
    z-index: 1;
    font-weight: 600;
    font-size: 15px;
    color: black;
    padding: 15px 12px;
    border-bottom: 2px solid #dee2e6;
    font-family: 'Segoe UI', sans-serif;
  }

  .userTable td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    text-align: left;
    box-sizing: border-box;
    height: 60px;
    font-size: 13px;
    font-weight: 400;
  }

  /* Add bottom padding for the last record */
  .userTable tbody tr:last-child td {
    padding-bottom: 20px;
  }

  /* Custom scrollbar styles */
  .userTableContainer::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .userTableContainer::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .userTableContainer::-webkit-scrollbar-thumb {
    background: #127C96;
    border-radius: 3px;
  }

  .userTableContainer::-webkit-scrollbar-thumb:hover {
    background: #0d5a6e;
  }

  .userInfo {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 100%;
  }

  .avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #127C96;
  }

  .badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    color: white;
    text-transform: capitalize;
  }

  /* Department Badges */
  .dept1 {
    color: #000000;
    /* background-color: #f26d6d; */
  }

  .dept2 {
    color: #000000;

    /* background-color: #9c88ff; */
  }

  .dept3 {
    color: #000000;

    /* background-color: #6dcff6; */
  }

  /* Role Badges */
  .admin {
    color: #000000;

    /* background-color: #ff7675; */
  }

  .user {
    /* background-color: #55efc4; */
    color: #2d3436;
  }

  .mentor {
    /* background-color: #ffeaa7; */
    color: #2d3436;
  }

  .actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
  }

  .editBtn, .deleteBtn {
    background: transparent !important;
    border: none;
    padding: 4px;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    width: auto;
    height: auto;
    min-width: 20px;
    min-height: 20px;
    transition: all 0.3s ease;
  }

  .editBtn:hover, .deleteBtn:hover {
    border-radius: 4px;
    transform: scale(1.1);
  }

  .actions .editIcon,
  .actions .deleteIcon {
    font-size: 18px;
    min-width: 20px;
    min-height: 15px;
    cursor: pointer;
    transition: transform 0.2s ease, color 0.2s ease;
    background: none !important;
    border-radius: 0;
  }

  .actions svg {
    font-size: 18px;
    cursor: pointer;
    transition: transform 0.2s ease, color 0.2s ease;
  }

  .actions svg:hover {
    transform: scale(1.2);
  }

  .actions .editIcon {
    color: #000000;
  }

  .actions .editIcon:active {
    transform: scale(0.95);
    color: #000000;
  }

  .actions .deleteIcon {
    color: #4d91a1;
  }

  .actions .deleteIcon:active {
    transform: scale(0.95);
    color: #65aec0;
  }

  .spinnerCell {
    height: 100px;
    text-align: center;
    vertical-align: middle;
    position: relative;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #127C96;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  @keyframes spin {
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  .pagination {
    margin-bottom: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
    min-width: 40px;
    text-align: center;
    white-space: nowrap;
  }

  .pagination button:hover:not(.active) {
    background-color: #f1f1f1;
  }

  .pagination button.active {
    background-color: #127C96;
    color: white;
    border-color: #127C96;
  }

  .pagination button:disabled {
    background-color: #f1f1f1;
    cursor: not-allowed;
    opacity: 0.6;
  }

  @media (max-width: 768px) { /* Tablet size and below */
    .actions .editIcon,
    .actions .deleteIcon {
      font-size: 24px; /* Keep the size same for tablet */
    }
  }

  @media (max-width: 480px) { /* Mobile size */
    .actions .editIcon,
    .actions .deleteIcon {
      font-size: 24px; /* Keep the size same for mobile */
    }
  }

  .statusBadge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    min-width: 90px;
    text-align: center;
  }

  .pending {
    background-color: #FFF3E0;
    color: #E65100;
    border: 1px solid #FFB74D;
  }

  .inProgress {
    background-color: #E3F2FD;
    color: #1565C0;
    border: 1px solid #64B5F6;
  }

  .completed {
    background-color: #E8F5E9;
    color: #2E7D32;
    border: 1px solid #81C784;
  }
  
  .loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #127C96;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 50px auto;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }