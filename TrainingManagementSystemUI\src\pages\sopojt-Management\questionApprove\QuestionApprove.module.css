.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.questionApproveMaster {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.controls input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
}

.userTableContainer {
  margin-top: 20px;
  flex-grow: 1;
  height: 70%; /* Adjust height as needed */
  overflow: auto;
  position: relative;
  padding-bottom: 20px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.userTableContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.userTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.userTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 12px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
}

.userTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
}


.userTable th:nth-child(1), .userTable td:nth-child(1) {
  min-width: 200px;
  width: 30%;
}
.userTable th:nth-child(2), .userTable td:nth-child(2) {
  min-width: 150px;
  width: 25%;
}
.userTable th:nth-child(3), .userTable td:nth-child(3) {
  min-width: 120px;
  width: 15%;
  text-align: center;
}
.userTable th:nth-child(4), .userTable td:nth-child(4) {
  min-width: 200px;
  width: 30%;
  text-align: center;
}

/* Add bottom padding for the last record */
.userTable tbody tr:last-child td {
  padding-bottom: 20px;
}

/* Custom scrollbar styles */
.userTableContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.userTableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.userTableContainer::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 3px;
}

.userTableContainer::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

.actions {
  display: flex;
  gap: 5px;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 2px;
  box-sizing: border-box;
}

.actionDivider {
  display: inline-block;
  width: 1.5px;
  height: 28px;
  background: #bdbdbd;
  border-radius: 1px;
  vertical-align: middle;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  background: transparent;
  border: none;
  padding: 2px;
  cursor: pointer;
  min-width: 60px;
}

.actionButton span {
  font-size: 11px; /* Smaller font for text */
  color: #666; /* Grey text color */
  white-space: nowrap; /* Prevent text wrapping */
}

.approveBtn, .rejectBtn { /* Remove original styles for these buttons */
  /* background-color: #137688;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer; */
  display: none; /* Hide the original buttons */
}

.approveIcon,
.rejectIcon {
  font-size: 18px; /* Icon size */
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
}

.approveIcon {
  color: #28a745; /* Green color for approve */
}

.rejectIcon {
  color: #dc3545; /* Red color for reject */
}

.returnIcon {
  color: #ffc107; /* Yellow color for return */
}

.actions svg:hover {
  transform: scale(1.2);
}

.actions svg:active {
  transform: scale(0.95);
}

/* Spinner styles */
.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Style for the Actions column header and cells */
.userTable th:nth-child(4) { /* Actions column header */
  text-align: center;
  vertical-align: middle; /* Vertically center the header content */
}

/* Style for the Actions column cells */
.userTable td:nth-child(4) {
    text-align: center; /* Center content in Actions column */
    vertical-align: middle; /* Vertically align content in Actions column */
}

.reasonInput {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reasonInput label {
  font-weight: 500;
  color: #333;
}

.reasonInput textarea {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.reasonInput textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.viewButton {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle; /* Vertically center the button */
}

.viewIcon {
  color: #127C96;
  font-size: 18px;
  transition: transform 0.2s ease;
  display: block;
  margin: 0 auto;
}

.viewIcon:hover {
  transform: scale(1.2);
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.viewModal {
  width: 90%;
  height: 90%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modalHeader h2 {
  margin: 0;
  font-size: 20px;
  color: #2c3e50;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: #e9ecef;
  color: #333;
}

.viewModalContent {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
  background: white;
  position: relative;
}

.questionsList {
  display: flex;
  flex-direction: column;
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.questionItem {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.questionHeader {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.questionNumber {
  font-weight: 600;
  color: #127C96;
  font-size: 15px;
  min-width: 40px;
}

.questionText {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  color: #2c3e50;
}

.questionMarks {
  color: #666;
  font-size: 13px;
  white-space: nowrap;
  background: #e9ecef;
  padding: 4px 10px;
  border-radius: 6px;
}

.mandatoryBadge {
  background: #dc3545;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  white-space: nowrap;
  font-weight: 500;
}

.optionsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-left: 40px;
}

.optionItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background: white;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.optionItem:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.correctOption {
  background: #e8f5e9;
  border-color: #28a745;
}

.optionText {
  flex: 1;
  font-size: 13px;
  color: #2c3e50;
  line-height: 1.4;
}

.correctBadge {
  background: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  white-space: nowrap;
  font-weight: 500;
}

.loadingSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loadingSpinner::before {
  content: '';
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingSpinner::after {
  content: 'Loading questions...';
  color: #666;
  font-size: 16px;
}

.noQuestions {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
  font-size: 16px;
}

/* Custom scrollbar for the modal content */
.viewModalContent::-webkit-scrollbar {
  width: 8px;
}

.viewModalContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.viewModalContent::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 4px;
}

.viewModalContent::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

.arrowButton:hover {
  background: rgba(18, 124, 150, 1);
}

/* Status Badge Styles */
.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.statusDraft {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.statusApproved {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.statusReturn {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.statusReject {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Tooltip styles */
.tooltipWrapper {
  position: relative;
  display: inline-block;
  overflow: visible !important;
  z-index: 10;
}

.tooltipText {
  visibility: hidden;
  background-color: rgba(47, 157, 200, 0.909);
  color: white;
  text-align: left;
  padding: 20px 20px;
  border-radius: 4px;
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: normal;
  word-wrap: break-word;
  max-width: 250px;
  z-index: 9999;
  min-width: 150px;
  pointer-events: none;
}

.tooltipWrapper:hover .tooltipText {
  visibility: visible;
  opacity: 1;
  pointer-events: auto;
}

.viewBtn,
.downloadBtn {
  background-color: #127C96;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
}

.editBtn, .deleteBtn, .viewBtn, .actionButton, .approveBtn, .rejectBtn, .returnBtn {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
}
.editBtn:focus, .deleteBtn:focus, .viewBtn:focus, .actionButton:focus, .approveBtn:focus, .rejectBtn:focus, .returnBtn:focus,
.editBtn:active, .deleteBtn:active, .viewBtn:active, .actionButton:active, .approveBtn:active, .rejectBtn:active, .returnBtn:active,
.editBtn:hover, .deleteBtn:hover, .viewBtn:hover, .actionButton:hover, .approveBtn:hover, .rejectBtn:hover, .returnBtn:hover {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}


