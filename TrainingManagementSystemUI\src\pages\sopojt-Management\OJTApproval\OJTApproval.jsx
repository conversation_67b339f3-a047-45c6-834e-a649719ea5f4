import React, { useState, useEffect, useRef } from 'react';
import { FaCheck, FaTimes, FaEye, FaDownload, FaUndo } from 'react-icons/fa';
import styles from './OJTApproval.module.css';
import Pagination from '../../../components/pagination/Pagination';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';
import { fetchOJTByUserId, updateOJTStatus, onjobtrainingDownloadById } from '../../../services/sopojt-Management/OJTMasterService';
import { downloadFileToBrowserById } from '../../../services/DownloadService';
import FileViewer from '../../../components/common/fileViewer/FileViewer';

const OJTApproval = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [ojtData, setOjtData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [selectedOJT, setSelectedOJT] = useState(null);
  const [approvalRemarks, setApprovalRemarks] = useState('');
  const [rejectionRemarks, setRejectionRemarks] = useState('');
  const [returnRemarks, setReturnRemarks] = useState('');
  const [showLeftIndicator, setShowLeftIndicator] = useState(false);
  const [showRightIndicator, setShowRightIndicator] = useState(true);
  const [showFileViewer, setShowFileViewer] = useState(false);
  const [pdfFileId, setPdfFileId] = useState(null);
  const [pdfFileType, setPdfFileType] = useState(null);
  const [pdfFileExtension, setPdfFileExtension] = useState(null);
  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm,
          showOnlyUnderReview: true
        };
        const res = await fetchOJTByUserId(payload);
        setOjtData(res.onJobTrainingMaster || []);
        setTotalRecords(res.totalRecord || 0);
        console.log(res);
      } catch {
        setOjtData([]);
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  const checkScroll = () => {
    if (tableContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tableContainerRef.current;
      const isAtStart = scrollLeft <= 0;
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1;
      
      setShowLeftIndicator(!isAtStart);
      setShowRightIndicator(!isAtEnd);
    }
  };

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener('scroll', checkScroll);
      window.addEventListener('resize', checkScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      }
    };
  }, []);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1);
  };

  const handleApprove = (ojt) => {
    setSelectedOJT(ojt);
    setShowApproveModal(true);
  };

  const handleReject = (ojt) => {
    setSelectedOJT(ojt);
    setShowRejectModal(true);
  };

  const handleReturn = (ojt) => {
    setSelectedOJT(ojt);
    setShowReturnModal(true);
  };

  const handleView = (ojt) => {
    setPdfFileId(ojt.onJobTrainingID);
    setPdfFileType('ojt');
    setPdfFileExtension(ojt.associatedDocumentExtention);
    setShowFileViewer(true);
  };

  const handleDownload = async (ojt) => {
    try {
      // console.log(documentId);
      await downloadFileToBrowserById('ojt',ojt.onJobTrainingID);

    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const handleStatusUpdate = async (status, remarks = '') => {
    if (!remarks.trim()) {
      toast.error(`Please provide ${status.toLowerCase()} remarks`);
      return;
    }

    try {
      const userID = sessionStorage.getItem('userID');
      const res = await updateOJTStatus({
        onJobTrainingID: selectedOJT.onJobTrainingID,
        ojtStatus: status,
        modifiedBy: userID,
        reasonForChange: remarks
      });
      if (res.header && res.header.errorCount === 0) {
        toast.success(res.header.messages?.[0]?.messageText || `OJT ${status.toLowerCase()}d successfully`);
        // Refresh list
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: searchTerm,
          showOnlyUnderReview: false
        };
        const fetchRes = await fetchOJTByUserId(payload);
        setOjtData(fetchRes.onJobTrainingMaster || []);
        setTotalRecords(fetchRes.totalRecord || 0);
      } else {
        toast.error(res.header?.messages?.[0]?.messageText || `Failed to ${status.toLowerCase()} OJT`);
      }
      if (status === 'Rejected') {
        setShowRejectModal(false);
        setRejectionRemarks('');
      } else if (status === 'Returned') {
        setShowReturnModal(false);
        setReturnRemarks('');
      } else {
        setShowApproveModal(false);
        setApprovalRemarks('');
      }
      setSelectedOJT(null);
    } catch (error) {
      console.error(`Error ${status.toLowerCase()}ing OJT:`, error);
      toast.error(`Failed to ${status.toLowerCase()} OJT`);
    }
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      {showFileViewer && (
        <FileViewer
          id={pdfFileId}
          type={pdfFileType}
          extension={pdfFileExtension}
          onClose={() => setShowFileViewer(false)}
        />
      )}

      {showApproveModal && (
        <Modal
          title="Confirm Approval"
          message={
            <div>
              <p>Are you sure you want to approve the OJT "{selectedOJT?.onJobTrainingTitle}" ({selectedOJT?.onJobTrainingCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="approveReason">Reason for approval:</label>
                <textarea
                  id="approveReason"
                  value={approvalRemarks}
                  onChange={(e) => setApprovalRemarks(e.target.value)}
                  placeholder="Please provide a reason for approval"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Approved', approvalRemarks)}
          onCancel={() => {
            setShowApproveModal(false);
            setSelectedOJT(null);
            setApprovalRemarks('');
          }}
        />
      )}

      {showRejectModal && (
        <Modal
          title="Confirm Rejection"
          message={
            <div>
              <p>Are you sure you want to reject the OJT "{selectedOJT?.onJobTrainingTitle}" ({selectedOJT?.onJobTrainingCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="rejectReason">Reason for rejection:</label>
                <textarea
                  id="rejectReason"
                  value={rejectionRemarks}
                  onChange={(e) => setRejectionRemarks(e.target.value)}
                  placeholder="Please provide a reason for rejection"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Rejected', rejectionRemarks)}
          onCancel={() => {
            setShowRejectModal(false);
            setSelectedOJT(null);
            setRejectionRemarks('');
          }}
        />
      )}

      {showReturnModal && (
        <Modal
          title="Confirm Return"
          message={
            <div>
              <p>Are you sure you want to return the OJT "{selectedOJT?.onJobTrainingTitle}" ({selectedOJT?.onJobTrainingCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="returnReason">Reason for return:</label>
                <textarea
                  id="returnReason"
                  value={returnRemarks}
                  onChange={(e) => setReturnRemarks(e.target.value)}
                  placeholder="Please provide a reason for return"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Returned', returnRemarks)}
          onCancel={() => {
            setShowReturnModal(false);
            setSelectedOJT(null);
            setReturnRemarks('');
          }}
        />
      )}

      <div className={styles.container}>
        <div className={styles.documentReview}>
          <div className={styles.panelHeader}>
            <h2>On Job Training Approval</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearch}
                className={styles.searchInput}
              />
            </div>
          </div>

          <div ref={tableContainerRef} className={styles.docTableContainer}>
            <div className={styles.tableWrapper}>
              <table className={styles.docTable}>
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Code</th>
                    <th>Evaluation Type</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="7" className={styles.spinnerCell}>
                        <div className={styles.spinner}></div>
                      </td>
                    </tr>
                  ) : ojtData.length > 0 ? (
                    ojtData.map((item) => (
                      <tr key={item.onJobTrainingID}>
                        <td>{item.onJobTrainingTitle}</td>
                        <td>{item.onJobTrainingCode}</td>
                        <td>{item.evaluationType}</td>
                        <td>
                          <div className={styles.actions}>
                            <button
                              className={styles.actionButton}
                              onClick={() => handleView(item)}
                              title="View OJT"
                              disabled={!item.associatedDocumentPath}
                            >
                              <FaEye className={styles.viewIcon} />
                              <span>View</span>
                            </button>
                            <button
                              className={styles.actionButton}
                              onClick={() => handleDownload(item)}
                              title="Download OJT"
                              disabled={!item.associatedDocumentPath}
                            >
                              <FaDownload className={styles.downloadIcon} />
                              <span>Download</span>
                            </button>
                            <span className={styles.actionDivider}></span>
                            <button
                              className={styles.actionButton}
                              onClick={() => handleApprove(item)}
                              title="Approve OJT"
                            >
                              <FaCheck className={styles.approveIcon} />
                              <span>Approve</span>
                            </button>
                            <button
                              className={styles.actionButton}
                              onClick={() => handleReturn(item)}
                              title="Return OJT"
                            >
                              <FaUndo className={styles.returnIcon} />
                              <span>Return</span>
                            </button>
                            <button
                              className={styles.actionButton}
                              onClick={() => handleReject(item)}
                              title="Reject OJT"
                            >
                              <FaTimes className={styles.rejectIcon} />
                              <span>Reject</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="7" style={{ textAlign: 'center', padding: '20px' }}>
                        No OJT records found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default OJTApproval;
