import React, { useState, useEffect, useContext } from "react";
import styles from "./RoleMaster.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FaEdit } from "react-icons/fa";
import { FcCancel } from "react-icons/fc";
import { useNavigate } from "react-router-dom";
import { RoleContext } from "../../../context/RoleContext";
import { fetchAllRoles, deleteRole } from "../../../services/systemAdmin/RoleMasterService";
import Pagination from "../../../components/pagination/Pagination";
import Modal from "../../../components/common/Modal";

const RoleMaster = () => {
  const navigate = useNavigate();
  const [roles, setRoles] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(true);
  const itemsPerPage = 10;
  const [showModal, setShowModal] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState(null);
  const { setRoleDetails } = useContext(RoleContext);
  const [refreshKey, setRefreshKey] = useState(0);

  // Debounce search term
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on new search
    }, 500); // 500ms debounce delay

    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Fetch roles with search and pagination
  useEffect(() => {
    const loadRoles = async () => {
      setLoading(true);
      try {
        const data = await fetchAllRoles(currentPage, itemsPerPage, debouncedSearchTerm);

        if (data.header?.errorCount === 0 && Array.isArray(data.roles)) {
          setRoles(data.roles);
          setTotalRecords(data.totalRecord || 0);
        } else {
          toast.error(data.header?.messages?.[0]?.messageText || "Failed to load roles");
        }
      } catch (error) {
        console.error("Error fetching roles:", error);
        toast.error("An error occurred while fetching roles");
      } finally {
        setLoading(false);
      }
    };

    loadRoles();
  }, [currentPage, debouncedSearchTerm, refreshKey]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const navigateTo = (path, state = {}) => {
    navigate(path, { state });
  };

  const handleEditRole = (role) => {
    setRoleDetails(role.roleID, role.roleName, role.description);
    navigateTo("/system-admin/role-master/edit-role", { roleData: role });
  };

  const handleDeleteRole = (role) => {
    setRoleToDelete(role);
    setShowModal(true);
  };
  
  const confirmDeleteRole = async () => {
    try {
      const result = await deleteRole(roleToDelete.roleID);
      if (result.header?.errorCount === 0) {
        toast.success(result.header?.messages?.[0]?.messageText || "Role deleted successfully");
        setCurrentPage(1);
        setRefreshKey(prev => prev + 1);
      } else {
        toast.error(result.header?.messages?.[0]?.messageText || "Failed to delete role");
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error("Error deleting role. Please try again.");
    } finally {
      setShowModal(false);
      setRoleToDelete(null);
    }
  };
  
  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      
      <div className={styles.container}>
        <div className={styles.userMaster}>
          <div className={styles.panelHeader}>
            <h2>Role Master</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
              <button
                className={styles.addUserBtn}
                onClick={() => navigateTo("/system-admin/role-master/add-role")}
              >
                + Add
              </button>
            </div>
          </div>

          <div className={styles.userTableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th>Role Name</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="2" className={styles.spinnerCell}>
                      <div className={styles.spinner}></div>
                    </td>
                  </tr>
                ) : roles.length > 0 ? (
                  roles.map((item) => (
                    <tr key={item.roleID}>
                      <td>{item.roleName}</td>
                      <td>
                        <div className={styles.actions}>
                          <button 
                            className={styles.editBtn} 
                            onClick={() => handleEditRole(item)}
                            title="Edit Role"
                          >
                            <FaEdit className={styles.editIcon} />
                          </button>
                          <button 
                            className={styles.deleteBtn} 
                            onClick={() => handleDeleteRole(item)}
                            title="Delete Role"
                          >
                            <FcCancel className={styles.deleteIcon} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="2" style={{ textAlign: 'center', padding: '20px' }}>
                      No roles found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
  currentPage={currentPage}
  totalPages={totalPages}
  paginate={paginate}
/>
        </div>
      </div>

      {showModal && roleToDelete && (
  <Modal
    title="Confirm Delete"
    message={`Are you sure you want to delete "${roleToDelete.roleName}"?`}
    onConfirm={confirmDeleteRole}
    onCancel={() => setShowModal(false)}
  />
)}
    </>
  );
};

export default RoleMaster;
