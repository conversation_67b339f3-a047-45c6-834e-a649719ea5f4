import React, { useState, useEffect, useContext, useRef } from 'react';
import styles from './EditUser.module.css';

import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import AsyncSelect from 'react-select/async';
import { UserContext } from '../../../../context/UserContext';
import { fetchUsersBasicInfo, updateUserBasicInfo, fetchDesignationsWithSearch, fetchDepartmentsWithSearch, fetchRolesWithSearch, fetchUsersWithSearch } from '../../../../services/systemAdmin/UserMasterService';
import { fetchAllDepartments } from '../../../../services/systemAdmin/DepartmentMasterService';
import { fetchAllDesignations } from '../../../../services/systemAdmin/DesignationMasterService';
import { fetchAllRoles } from '../../../../services/systemAdmin/RoleMasterService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import Modal from '../../../../components/common/Modal';

const EditUser = () => {
  const navigate = useNavigate();
  const { userDetails } = useContext(UserContext);
  const initialFormDataRef = useRef(null);

  const [loading, setLoading] = useState(true);

  // State for designation dropdown
  const [designationOptions, setDesignationOptions] = useState([]);
  const [designationSearch, setDesignationSearch] = useState('');
  const [designationPage, setDesignationPage] = useState(0);
  const [hasMoreDesignations, setHasMoreDesignations] = useState(true);
  const [isLoadingDesignations, setIsLoadingDesignations] = useState(false);

  // State for department dropdown
  const [departmentOptions, setDepartmentOptions] = useState([]);
  const [departmentSearch, setDepartmentSearch] = useState('');
  const [departmentPage, setDepartmentPage] = useState(0);
  const [hasMoreDepartments, setHasMoreDepartments] = useState(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);

  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');


  // State for role dropdown
  const [roleOptions, setRoleOptions] = useState([]);
  const [roleSearch, setRoleSearch] = useState('');
  const [rolePage, setRolePage] = useState(0);
  const [hasMoreRoles, setHasMoreRoles] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);

  // State for user dropdown
  const [userOptions, setUserOptions] = useState([]);
  const [userSearch, setUserSearch] = useState('');
  const [userPage, setUserPage] = useState(0);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  const [formData, setFormData] = useState({
    userID: '',
    EmployeeID: '',
    FirstName: '',
    LastName: '',
    Gender: '',
    CategoryType: '',
    RoleID: '',
    DepartmentID: '',
    DesignationID: '',
    ReportsTo: '',
    EmailID: '',
    LoginID: '',
    InductionRequire: false,
    UserProfileID: '',
  });

  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '200px',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: '200px',
      overflowY: 'auto',
      '&::-webkit-scrollbar': {
        width: '8px'
      },
      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1'
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#888',
        borderRadius: '4px'
      },
      '&::-webkit-scrollbar-thumb:hover': {
        background: '#555'
      }
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isFocused ? '#e6f3ff' : 'white',
      color: '#333',
      '&:hover': {
        backgroundColor: '#e6f3ff'
      }
    })
  };

  // Function to load initial designations
  const loadInitialDesignations = async () => {
    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(0, 10, '');
      if (response.designations) {
        const options = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        setDesignationOptions(options);
        setHasMoreDesignations(response.totalRecord > 10);
        setDesignationPage(1);
      }
    } catch (error) {
      console.error('Error loading initial designations:', error);
      toast.error('Error loading designations');
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  // Function to load initial departments
  const loadInitialDepartments = async () => {
    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(0, 10, '');
      if (response.departments) {
        const options = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        setDepartmentOptions(options);
        setHasMoreDepartments(response.totalRecord > 10);
        setDepartmentPage(1);
      }
    } catch (error) {
      console.error('Error loading initial departments:', error);
      toast.error('Error loading departments');
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Function to load initial roles
  const loadInitialRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(0, 10, '');
      if (response.roles) {
        const options = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        setRoleOptions(options);
        setHasMoreRoles(response.totalRecord > 10);
        setRolePage(1);
      }
    } catch (error) {
      console.error('Error loading initial roles:', error);
      toast.error('Error loading roles');
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Function to load initial users
  const loadInitialUsers = async () => {
    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersWithSearch(0, 10, '');
      if (response.usersBasicInfo) {
        const options = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        setUserOptions(options);
        setHasMoreUsers(response.totalRecord > 10);
        setUserPage(1);
      }
    } catch (error) {
      console.error('Error loading initial users:', error);
      toast.error('Error loading users');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Load initial data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Load initial data for all dropdowns
        await Promise.all([
          loadInitialDesignations(),
          loadInitialDepartments(),
          loadInitialRoles(),
          loadInitialUsers()
        ]);

        // Initialize form data
        const storedFormData = JSON.parse(localStorage.getItem('editUserFormData'));
        const initialData = storedFormData || userDetails;

        if (initialData) {
          const formattedData = {
            userID: initialData.userID,
            EmployeeID: initialData.employeeID || '',
            FirstName: initialData.firstName || '',
            LastName: initialData.lastName || '',
            Gender: initialData.gender || '',
            CategoryType: initialData.categoryType || '',
            RoleID: initialData.roleID || '',
            DepartmentID: initialData.departmentID || '',
            DesignationID: initialData.designationID || '',
            ReportsTo: initialData.reportsTo || '',
            EmailID: initialData.emailID || '',
            LoginID: initialData.loginID || '',
            InductionRequire: initialData.inductionRequire === 'True' || false,
            UserProfileID: initialData.userProfileID || '',
          };

          setFormData(formattedData);
          initialFormDataRef.current = { ...formattedData };
        }
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load user data');        

      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userDetails]);

  // Function to load more designations when scrolling
  const loadMoreDesignations = async () => {
    if (!hasMoreDesignations || isLoadingDesignations) return;

    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(designationPage * 10, 10, designationSearch);
      
      if (response.designations && response.designations.length > 0) {
        const newOptions = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        
        const existingIds = new Set(designationOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setDesignationOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreDesignations(response.totalRecord > (designationPage + 1) * 10);
          setDesignationPage(prev => prev + 1);
        } else {
          setHasMoreDesignations(false);
        }
      } else {
        setHasMoreDesignations(false);
      }
    } catch (error) {
      console.error('Error loading more designations:', error);
      toast.error('Error loading more designations');
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  // Function to load more departments when scrolling
  const loadMoreDepartments = async () => {
    if (!hasMoreDepartments || isLoadingDepartments) return;

    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(departmentPage * 10, 10, departmentSearch);
      
      if (response.departments && response.departments.length > 0) {
        const newOptions = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        
        const existingIds = new Set(departmentOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setDepartmentOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreDepartments(response.totalRecord > (departmentPage + 1) * 10);
          setDepartmentPage(prev => prev + 1);
        } else {
          setHasMoreDepartments(false);
        }
      } else {
        setHasMoreDepartments(false);
      }
    } catch (error) {
      console.error('Error loading more departments:', error);
      toast.error('Error loading more departments');
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Function to load more roles when scrolling
  const loadMoreRoles = async () => {
    if (!hasMoreRoles || isLoadingRoles) return;

    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(rolePage * 10, 10, roleSearch);
      
      if (response.roles && response.roles.length > 0) {
        const newOptions = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        
        const existingIds = new Set(roleOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setRoleOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreRoles(response.totalRecord > (rolePage + 1) * 10);
          setRolePage(prev => prev + 1);
        } else {
          setHasMoreRoles(false);
        }
      } else {
        setHasMoreRoles(false);
      }
    } catch (error) {
      console.error('Error loading more roles:', error);
      toast.error('Error loading more roles');
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Function to load more users when scrolling
  const loadMoreUsers = async () => {
    if (!hasMoreUsers || isLoadingUsers) return;

    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersWithSearch(userPage * 10, 10, userSearch);
      
      if (response.usersBasicInfo && response.usersBasicInfo.length > 0) {
        const newOptions = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        
        const existingIds = new Set(userOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setUserOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreUsers(response.totalRecord > (userPage + 1) * 10);
          setUserPage(prev => prev + 1);
        } else {
          setHasMoreUsers(false);
        }
      } else {
        setHasMoreUsers(false);
      }
    } catch (error) {
      console.error('Error loading more users:', error);
      toast.error('Error loading more users');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Functions to load options for search
  const loadDesignations = async (inputValue, callback) => {
    if (!inputValue) {
      callback(designationOptions);
      return;
    }

    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(0, 10, inputValue);
      
      if (response.designations) {
        const options = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        setDesignationOptions(options);
        setDesignationPage(1);
        setHasMoreDesignations(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading designations:', error);
      toast.error('Error loading designations');
      callback([]);
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  const loadDepartments = async (inputValue, callback) => {
    if (!inputValue) {
      callback(departmentOptions);
      return;
    }

    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(0, 10, inputValue);
      
      if (response.departments) {
        const options = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        setDepartmentOptions(options);
        setDepartmentPage(1);
        setHasMoreDepartments(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading departments:', error);
      toast.error('Error loading departments');
      callback([]);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const loadRoles = async (inputValue, callback) => {
    if (!inputValue) {
      callback(roleOptions);
      return;
    }

    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(0, 10, inputValue);
      
      if (response.roles) {
        const options = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        setRoleOptions(options);
        setRolePage(1);
        setHasMoreRoles(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      toast.error('Error loading roles');
      callback([]);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const loadUsers = async (inputValue, callback) => {
    if (!inputValue) {
      callback(userOptions);
      return;
    }

    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersWithSearch(0, 10, inputValue);
      
      if (response.usersBasicInfo) {
        const options = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        setUserOptions(options);
        setUserPage(1);
        setHasMoreUsers(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Error loading users');
      callback([]);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    };
    setFormData(updatedFormData);
    localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    const requiredFields = [
      'FirstName', 'LastName', 'Gender', 'EmployeeID', 'RoleID',
      'DepartmentID', 'DesignationID', 'EmailID', 'LoginID'
    ];

    for (let field of requiredFields) {
      if (!formData[field]) {
        toast.error(`Please fill in the ${field} field`);
        return;
      }
    }

    // Validate gender
    if (!['M', 'F', 'O'].includes(formData.Gender)) {
      toast.error('Please select a valid gender');
      return;
    }

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }

    setLoading(true);

    try {
      const payload = {
        userID: formData.userID,
        employeeID: formData.EmployeeID.trim(),
        firstName: formData.FirstName.trim(),
        lastName: formData.LastName.trim(),
        gender: formData.Gender,
        categoryType: formData.CategoryType || '',
        roleID: Number(formData.RoleID),
        departmentID: Number(formData.DepartmentID),
        designationID: Number(formData.DesignationID),
        reportsTo: formData.ReportsTo ? Number(formData.ReportsTo) : 0,
        emailID: formData.EmailID.trim(),
        loginID: formData.LoginID.trim(),
        inductionRequire: formData.InductionRequire || false,
        userProfileID: Number(formData.UserProfileID) || 0,
        modifiedBy: sessionStorage.getItem('userId') || 'Admin',
        reasonForChange: reasonForChange,
        electronicSignature: sessionStorage.getItem('userId') || 'Admin',
        signatureDate: new Date().toISOString(),
        plantID: Number(sessionStorage.getItem('plantId')) || 0
      };

      const response = await updateUserBasicInfo(payload);

      if (response.header?.errorCount === 0) {
        toast.success('User details updated successfully!');
        localStorage.removeItem('editUserFormData');
        
        setTimeout(() => {
          navigate('/system-admin/user-master');
        }, 3000);
      } else {
        const errorMsg = response.header?.messages?.[0]?.messageText || 'Failed to update user details';
        toast.error(errorMsg);
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast.error('Failed to update user details. Please try again.');
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  if (loading) {
    return (
      <>
        <ToastContainer
          position="top-center"
          autoClose={1500}
          hideProgressBar={false}
          newestOnTop={true}
          closeOnClick={true}
          rtl={false}
          pauseOnFocusLoss={true}
          draggable={true}
          pauseOnHover={true}
          theme="colored"
          style={{
            zIndex: 9999,
            width: 'auto',
            maxWidth: '600px'
          }}
        />
        
        <div className={styles.container}>
          <form className={styles.form}>
            <div className="progress mb-3" style={{ height: '3px' }}>
              <div
                className="progress-bar progress-bar-striped progress-bar-animated"
                role="progressbar"
                style={{ width: '100%' }}
              />
            </div>
            <div className={styles.formContent}>
              <h3 className={styles.sectionHeading}>Edit User</h3>
            </div>
          </form>
        </div>
      </>
    );
  }

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          {loading && (
            <div className="progress mb-3" style={{ height: '3px' }}>
              <div
                className="progress-bar progress-bar-striped progress-bar-animated"
                role="progressbar"
                style={{ width: '100%' }}
              />
            </div>
          )}
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Edit User</h3>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>First Name <span style={{ color: 'red' }}>*</span></label>
                <input type="text" name="FirstName" value={formData.FirstName} onChange={handleChange} required />
              </div>
              <div className={styles.row}>
                <label>Last Name <span style={{ color: 'red' }}>*</span></label>
                <input type="text" name="LastName" value={formData.LastName} onChange={handleChange} required />
              </div>
            </div>

            <div className={styles.row}>
              <label>Gender <span style={{ color: 'red' }}>*</span></label>
              <Select
                name="Gender"
                options={[
                  { value: 'M', label: 'Male' },
                  { value: 'F', label: 'Female' },
                  { value: 'O', label: 'Other' }
                ]}
                defaultValue={formData.Gender ? { value: formData.Gender, label: { M: 'Male', F: 'Female', O: 'Other' }[formData.Gender] } : null}
                value={formData.Gender ? { value: formData.Gender, label: { M: 'Male', F: 'Female', O: 'Other' }[formData.Gender] } : null}
                onChange={(selected) => {
                  const updatedFormData = { ...formData, Gender: selected ? selected.value : '' };
                  setFormData(updatedFormData);
                  localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                }}
                className={styles.reactSelect}
                placeholder="-- Select Gender --"
                isSearchable={false}
              />
            </div>

            <div className={styles.row}>
              <label>Employee ID <span style={{ color: 'red' }}>*</span></label>
              <input type="text" name="EmployeeID" value={formData.EmployeeID} onChange={handleChange} required />
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Role <span style={{ color: 'red' }}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={roleOptions}
                  value={roleOptions.find((r) => r.value === Number(formData.RoleID))}
                  loadOptions={(inputValue, callback) => loadRoles(inputValue, callback)}
                  onChange={(selected) => {
                    const updatedFormData = { ...formData, RoleID: selected ? selected.value : '' };
                    setFormData(updatedFormData);
                    localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                  }}
                  onMenuOpen={() => {
                    if (roleOptions.length === 0) loadInitialRoles();
                  }}
                  onMenuScrollToBottom={loadMoreRoles}
                  onInputChange={(value) => {
                    setRoleSearch(value);
                    if (!value) {
                      setRolePage(0);
                      loadInitialRoles();
                    }
                  }}
                  isLoading={isLoadingRoles}
                  placeholder="-- Select Role --"
                  className={styles.reactSelect}
                  styles={customStyles}
                  menuPortalTarget={document.body}
                />
              </div>

              <div className={styles.row}>
                <label>Department <span style={{ color: 'red' }}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={departmentOptions}
                  value={departmentOptions.find((d) => d.value === Number(formData.DepartmentID))}
                  loadOptions={(inputValue, callback) => loadDepartments(inputValue, callback)}
                  onChange={(selected) => {
                    const updatedFormData = { ...formData, DepartmentID: selected ? selected.value : '' };
                    setFormData(updatedFormData);
                    localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                  }}
                  onMenuOpen={() => {
                    if (departmentOptions.length === 0) loadInitialDepartments();
                  }}
                  onMenuScrollToBottom={loadMoreDepartments}
                  onInputChange={(value) => {
                    setDepartmentSearch(value);
                    if (!value) {
                      setDepartmentPage(0);
                      loadInitialDepartments();
                    }
                  }}
                  isLoading={isLoadingDepartments}
                  placeholder="-- Select Department --"
                  className={styles.reactSelect}
                  styles={customStyles}
                  menuPortalTarget={document.body}
                />
              </div>
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Designation <span style={{ color: 'red' }}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={designationOptions}
                  value={designationOptions.find((d) => d.value === Number(formData.DesignationID))}
                  loadOptions={(inputValue, callback) => loadDesignations(inputValue, callback)}
                  onChange={(selected) => {
                    const updatedFormData = { ...formData, DesignationID: selected ? selected.value : '' };
                    setFormData(updatedFormData);
                    localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                  }}
                  onMenuOpen={() => {
                    if (designationOptions.length === 0) loadInitialDesignations();
                  }}
                  onMenuScrollToBottom={loadMoreDesignations}
                  onInputChange={(value) => {
                    setDesignationSearch(value);
                    if (!value) {
                      setDesignationPage(0);
                      loadInitialDesignations();
                    }
                  }}
                  isLoading={isLoadingDesignations}
                  placeholder="-- Select Designation --"
                  className={styles.reactSelect}
                  styles={customStyles}
                  menuPortalTarget={document.body}
                />
              </div>

              <div className={styles.row}>
                <label>Reports To <span style={{ color: 'red' }}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={userOptions}
                  value={userOptions.find((u) => u.value === formData.ReportsTo.toString())}
                  loadOptions={(inputValue, callback) => loadUsers(inputValue, callback)}
                  onChange={(selected) => {
                    const updatedFormData = { ...formData, ReportsTo: selected ? selected.value : '' };
                    setFormData(updatedFormData);
                    localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                  }}
                  onMenuOpen={() => {
                    if (userOptions.length === 0) loadInitialUsers();
                  }}
                  onMenuScrollToBottom={loadMoreUsers}
                  onInputChange={(value) => {
                    setUserSearch(value);
                    if (!value) {
                      setUserPage(0);
                      loadInitialUsers();
                    }
                  }}
                  isLoading={isLoadingUsers}
                  placeholder="-- Select Manager --"
                  className={styles.reactSelect}
                  styles={customStyles}
                  menuPortalTarget={document.body}
                />
              </div>
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Email <span style={{ color: 'red' }}>*</span></label>
                <input type="email" name="EmailID" value={formData.EmailID} onChange={handleChange} required />
              </div>
              <div className={styles.row}>
                <label>Login ID <span style={{ color: 'red' }}>*</span></label>
                <input type="text" name="LoginID" value={formData.LoginID} readOnly className={styles.readOnlyInput} />
              </div>
            </div>

            <div className={styles.row}>
              <label>Induction Required</label>
              <div className={styles.radioGroup}>
                <label>
                  <input
                    type="radio"
                    name="InductionRequire"
                    checked={formData.InductionRequire === true}
                    onChange={() => {
                      const updatedFormData = { ...formData, InductionRequire: true };
                      setFormData(updatedFormData);
                      localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                    }}
                  />
                  Yes
                </label>
                <label>
                  <input
                    type="radio"
                    name="InductionRequire"
                    checked={formData.InductionRequire === false}
                    onChange={() => {
                      const updatedFormData = { ...formData, InductionRequire: false };
                      setFormData(updatedFormData);
                      localStorage.setItem('editUserFormData', JSON.stringify(updatedFormData));
                    }}
                  />
                  No
                </label>
              </div>
            </div>

            {/* <div className={styles.row}>
              <label>User Profile ID</label>
              <input type="text" name="UserProfileID" value={formData.UserProfileID} onChange={handleChange} />
            </div> */}

          </div>

          <div className={styles.submitRow}>
            <button type="submit" className={styles.primaryBtn} disabled={loading}>
              {loading ? 'Updating...' : 'Update'}
            </button>
            <button type="button" onClick={() => navigate(-1)} className={styles.cancelBtn}>Cancel</button>
          </div>
        </form>
      </div>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the user "{formData.FirstName} {formData.LastName}"</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </>
  );
};

export default EditUser;