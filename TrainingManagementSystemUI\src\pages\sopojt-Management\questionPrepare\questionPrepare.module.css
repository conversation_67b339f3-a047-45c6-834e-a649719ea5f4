.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

/* .formCard, .questionPaperSection {
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
} */

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 1.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #333;
  box-sizing: border-box;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

/* .fieldHelper {
  font-size: 0.95rem;
  color: #888;
  margin-top: 0.2rem;
} */

.required {
  color: #e53935;
  margin-left: 2px;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input,
select,
textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

input[type="radio"] {
  appearance: none;
  color: #127c96;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #127c96;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  outline: none;
  transition: 0.2s all ease-in-out;
  background-color: white;
  background-image: none;
}

input[type="radio"]:checked {
  background-color: #127c96;
  border-color: #127c96;
}

input[type="radio"]:checked::before {
  content: '\2713';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
}

input[type="radio"]:focus {
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.6);
}

.inlineRow {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.inlineRow .row {
  width: 48%;
  padding-right: 10px;
}

.inlineRow .row:last-child {
  padding-right: 0;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

.optionRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.optionRow input {
  flex: 1;
}

.optionRow button {
  padding: 0.3rem 0.7rem;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.addBtn {
  background-color: #2196f3;
  color: #fff;
  border: none;
  border-radius: 5px;
  padding: 0.3rem 0.7rem;
  margin-top: 0.5rem;
  cursor: pointer;
}

.addOptionButton {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s;
  margin-top: 0.5rem;
}

.addOptionButton:hover {
  background-color: #0f6a83;
}

.charCount {
  font-size: 12px;
  color: #888;
  margin-left: 8px;
}

.buttonContainer {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 2rem;
  padding: 0 20px;
}

.actionButton {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #127C96;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButtonCancel {
  padding: 10px 20px;
  font-size: 14px;
  background-color: #d0d0d0;
  color: #000000;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  min-width: 120px;
  text-align: center;
}

.actionButton:hover {
  background-color: #0f6a83;
}

.actionButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Keep the type selector for backward compatibility or other buttons */
button[type="submit"] {
  /* Inherit styles from .actionButton or keep specific overrides if needed */
}

.formSection {
  background: #fff;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  margin-bottom: 2rem;
  /* border-top: 5px solid #127C96; */
}

.formSection h3 {
  color: #000000;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.questionForm {
  border: 1px solid #e0e0e0;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  background-color: #f9f9f9; /* Slightly different background for clarity */
  position: relative; /* Needed for absolute positioning of remove button */
}

.questionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
}

.questionHeader h5 {
  margin: 0;
  color: #00376e;
  font-size: 1.1rem;
  font-weight: 600;
}

.optionsColumn {
  display: flex;
  flex-direction: column;
}

.optionsColumn label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #000000;
}

.questionSettings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mandatoryRow {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
}

.mandatoryRow label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #000000;
  cursor: pointer;
}

.mandatoryRow input[type="checkbox"] {
  margin-right: 0.5rem;
  width: auto;
}

.questionForm .questionText label,
.questionForm .optionsList label,
.questionForm .correctAnswer label,
.questionForm .mandatory,
.questionForm .marks label {
   font-weight: 600;
   color: #000000;
   margin-bottom: 0.5rem;
   display: block;
}

.questionForm .optionsList input[type="text"],
.questionForm .correctAnswer select {
  width: calc(100% - 100px); /* Adjust width considering remove button */
  display: inline-block; /* Allow button next to it */
  margin-right: 10px;
}

.questionForm .optionItem {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.questionForm .optionItem input[type="text"] {
  flex-grow: 1;
  margin-right: 10px;
}

.questionForm .optionItem button {
  padding: 0.4rem 0.8rem;
  background-color: #f44336; /* Match Save Questions button */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
}

.questionForm .optionItem button:hover {
   background-color: #0f6a83; /* Match Save Questions button hover */
}

.questionForm .correctAnswer select {
   width: 100%;
}

.questionForm .mandatory {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.questionForm .marks {
   margin-bottom: 1rem;
}

.questionForm .actions button {
   padding: 0.4rem 0.8rem;
   background-color: #f44336;
   color: white;
   border: none;
   border-radius: 5px;
   cursor: pointer;
   font-size: 0.9rem;
}

.questionForm .actions button:hover {
    background-color: #d32f2f;
}

.addQuestion {
  text-align: center;
  margin-top: 2rem;
}

.addQuestion button {
  padding: 0.7rem 1.2rem;
  font-size: 1rem;
  background-color: #127C96; /* Match Save Questions button */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s;
}

.addQuestion button:hover {
  background-color: #0f6a83; /* Match Save Questions button hover */
}

.questionPaperSection {
  margin: 1.5rem auto;
}

.questionPaperTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 1.2rem;
  text-align: center;
}

.questionCard {
  margin-bottom: 1.2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
  color: #222;
}

.questionCardFullWidth {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  color: #333;
}

.questionText {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #222;
}

.optionsList {
  margin-left: 1.2rem;
  margin-bottom: 0.5rem;
  color: #222;
}

.optionItem {
  margin-bottom: 0.2rem;
  color: #444;
}

.correctOption {
  color: #000000;
  font-weight: 600;
}


@media (max-width: 900px) {
  .mainContentBg {
    padding-left: 0;
    padding: 16px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

@media (max-width: 700px) {
  .formCard {
    padding: 1.2rem 0.5rem;
    margin: 0 8px;
  }
  .mainContentBg {
    padding: 16px;
  }
  .formRow {
    flex-direction: column;
    gap: 0.5rem;
  }
  .fieldLabel {
    width: 100%;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
  color: #222;
}

.modalContent h3 {
  color: #127C96;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.reasonTextarea {
  width: 100%;
  padding: 10px;
  border: 1.5px solid #127C96;
  border-radius: 5px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  margin-bottom: 20px;
  box-sizing: border-box;
}

.modalButtons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.modalButtons .actionButton {
    min-width: 100px;
}

.cancelButton {
    padding: 12px 25px;
    font-size: 1rem;
    background-color: #e0e0e0;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.cancelButton:hover {
    background-color: #d5d5d5;
    border-color: #b0b0b0;
}

/* Document Registration Pattern Styles */
.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.primaryBtn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

@media (max-width: 768px) {
  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .questionCardFullWidth {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }
}
