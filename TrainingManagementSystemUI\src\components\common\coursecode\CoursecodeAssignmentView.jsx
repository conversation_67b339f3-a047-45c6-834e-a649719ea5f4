import React from 'react';
import styles from './coursecodeView.module.css';

const CoursecodeAssignmentView = ({ courseCodeDetails, onClose }) => {
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.viewModal}>
        <div className={styles.modalHeader}>
          <h2>
            <span className={styles.titleText}>{courseCodeDetails.courseTitle}</span>
            <span className={styles.codeText}>({courseCodeDetails.courseCode})</span>
          </h2>
          <button 
            className={styles.closeButton}
            onClick={onClose}
          >
            ×
          </button>
        </div>
        <div className={styles.viewModalContent}>
          <div className={styles.assignedUsersSection}>
            <h3>Assigned Users</h3>
            {courseCodeDetails.assignments && courseCodeDetails.assignments.length > 0 ? (
              <div className={styles.userGrid}>
                <div className={styles.userGridHeader}>
                  <div>User Name</div>
                  <div>Department Name</div>
                  <div>Designation</div>
                  <div>Role Name</div>
                </div>
                <div className={styles.userGridBody}>
                  {courseCodeDetails.assignments.map((assignment) => (
                    <div key={assignment.assignmentID} className={styles.userGridRow}>
                      <div>{`${assignment.firstName} ${assignment.lastName}`.trim()}</div>
                      <div>{assignment.departmentName || 'N/A'}</div>
                      <div>{'N/A'}</div>
                      <div>{assignment.roleName || 'N/A'}</div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className={styles.noAssignments}>No users assigned to this course code.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoursecodeAssignmentView; 