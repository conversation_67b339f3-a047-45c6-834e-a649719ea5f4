import React, { useState, useEffect, useRef } from 'react';
import styles from './AddUser.module.css';

import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import { fetchUsersBasicInfo, createUserBasicInfo, fetchDesignationsWithSearch, fetchDepartmentsWithSearch, fetchRolesWithSearch, fetchUsersWithSearch } from '../../../../services/systemAdmin/UserMasterService';
import { fetchAllDepartments } from '../../../../services/systemAdmin/DepartmentMasterService'
import {fetchAllDesignations} from '../../../../services/systemAdmin/DesignationMasterService';
import { fetchAllRoles } from '../../../../services/systemAdmin/RoleMasterService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import AsyncSelect from 'react-select/async';

const AddUser = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    EmployeeID: '',
    FirstName: '',
    LastName: '',
    Gender: '',
    CategoryType: '',
    RoleID: '',
    DepartmentID: '',
    DesignationID: '',
    ReportsTo: '',
    EmailID: '',
    LoginID: '',
    Password: '',
    ConfirmPassword: '',
    InductionRequire: false,
    UserProfileID: '',
  });

  const [roles, setRoles] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [designations, setDesignations] = useState([]);
  const [users, setUsers] = useState([]);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [designationOptions, setDesignationOptions] = useState([]);
  const [designationSearch, setDesignationSearch] = useState('');
  const [designationPage, setDesignationPage] = useState(0);
  const [hasMoreDesignations, setHasMoreDesignations] = useState(true);
  const [isLoadingDesignations, setIsLoadingDesignations] = useState(false);
  const [departmentOptions, setDepartmentOptions] = useState([]);
  const [departmentSearch, setDepartmentSearch] = useState('');
  const [departmentPage, setDepartmentPage] = useState(0);
  const [hasMoreDepartments, setHasMoreDepartments] = useState(true);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [roleOptions, setRoleOptions] = useState([]);
  const [roleSearch, setRoleSearch] = useState('');
  const [rolePage, setRolePage] = useState(0);
  const [hasMoreRoles, setHasMoreRoles] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [userOptions, setUserOptions] = useState([]);
  const [userSearch, setUserSearch] = useState('');
  const [userPage, setUserPage] = useState(0);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  const customStyles = {
    menu: (provided) => ({
      ...provided,
      maxHeight: '200px',
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: '200px',
      overflowY: 'auto',
      '&::-webkit-scrollbar': {
        width: '8px'
      },
      '&::-webkit-scrollbar-track': {
        background: '#f1f1f1'
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#888',
        borderRadius: '4px'
      },
      '&::-webkit-scrollbar-thumb:hover': {
        background: '#555'
      }
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isFocused ? '#e6f3ff' : 'white',
      color: '#333',
      '&:hover': {
        backgroundColor: '#e6f3ff'
      }
    })
  };

  // Function to load initial designations
  const loadInitialDesignations = async () => {
    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(0, 10, '');
      if (response.designations) {
        const options = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        setDesignationOptions(options);
        setHasMoreDesignations(response.totalRecord > 10);
        setDesignationPage(1);
      }
    } catch (error) {
      console.error('Error loading initial designations:', error);
      toast.error('Error loading designations');
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  // Function to load initial departments
  const loadInitialDepartments = async () => {
    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(0, 10, '');
      if (response.departments) {
        const options = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        setDepartmentOptions(options);
        setHasMoreDepartments(response.totalRecord > 10);
        setDepartmentPage(1);
      }
    } catch (error) {
      console.error('Error loading initial departments:', error);
      toast.error('Error loading departments');
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Function to load initial roles
  const loadInitialRoles = async () => {
    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(0, 10, '');
      if (response.roles) {
        const options = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        setRoleOptions(options);
        setHasMoreRoles(response.totalRecord > 10);
        setRolePage(1);
      }
    } catch (error) {
      console.error('Error loading initial roles:', error);
      toast.error('Error loading roles');
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Function to load initial users
  const loadInitialUsers = async () => {
    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersBasicInfo(0, 10, '');
      if (response.usersBasicInfo) {
        const options = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        setUserOptions(options);
        setHasMoreUsers(response.totalRecord > 10);
        setUserPage(1);
      }
    } catch (error) {
      console.error('Error loading initial users:', error);
      toast.error('Error loading users');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Load initial data when component mounts
  useEffect(() => {
    loadInitialDesignations();
    loadInitialDepartments();
    loadInitialRoles();
    loadInitialUsers();
  }, []);

  // Function to handle menu open
  const handleMenuOpen = () => {
    if (designationOptions.length === 0) {
      loadInitialDesignations();
    }
  };

  // Function to handle department menu open
  const handleDepartmentMenuOpen = () => {
    if (departmentOptions.length === 0) {
      loadInitialDepartments();
    }
  };

  // Function to handle role menu open
  const handleRoleMenuOpen = () => {
    if (roleOptions.length === 0) {
      loadInitialRoles();
    }
  };

  // Function to handle user menu open
  const handleUserMenuOpen = () => {
    if (userOptions.length === 0) {
      loadInitialUsers();
    }
  };

  // Function to load more designations when scrolling
  const loadMoreDesignations = async () => {
    if (!hasMoreDesignations || isLoadingDesignations) return;

    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(designationPage * 10, 10, designationSearch);
      
      if (response.designations && response.designations.length > 0) {
        const newOptions = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        
        // Prevent duplicate options
        const existingIds = new Set(designationOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setDesignationOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreDesignations(response.totalRecord > (designationPage + 1) * 10);
          setDesignationPage(prev => prev + 1);
        } else {
          setHasMoreDesignations(false);
        }
      } else {
        setHasMoreDesignations(false);
      }
    } catch (error) {
      console.error('Error loading more designations:', error);
      toast.error('Error loading more designations');
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  // Function to load more departments when scrolling
  const loadMoreDepartments = async () => {
    if (!hasMoreDepartments || isLoadingDepartments) return;

    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(departmentPage * 10, 10, departmentSearch);
      
      if (response.departments && response.departments.length > 0) {
        const newOptions = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        
        // Prevent duplicate options
        const existingIds = new Set(departmentOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setDepartmentOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreDepartments(response.totalRecord > (departmentPage + 1) * 10);
          setDepartmentPage(prev => prev + 1);
        } else {
          setHasMoreDepartments(false);
        }
      } else {
        setHasMoreDepartments(false);
      }
    } catch (error) {
      console.error('Error loading more departments:', error);
      toast.error('Error loading more departments');
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Function to load more roles when scrolling
  const loadMoreRoles = async () => {
    if (!hasMoreRoles || isLoadingRoles) return;

    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(rolePage * 10, 10, roleSearch);
      
      if (response.roles && response.roles.length > 0) {
        const newOptions = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        
        // Prevent duplicate options
        const existingIds = new Set(roleOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setRoleOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreRoles(response.totalRecord > (rolePage + 1) * 10);
          setRolePage(prev => prev + 1);
        } else {
          setHasMoreRoles(false);
        }
      } else {
        setHasMoreRoles(false);
      }
    } catch (error) {
      console.error('Error loading more roles:', error);
      toast.error('Error loading more roles');
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Function to load more users when scrolling
  const loadMoreUsers = async () => {
    if (!hasMoreUsers || isLoadingUsers) return;

    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersBasicInfo(userPage * 10, 10, userSearch);
      
      if (response.usersBasicInfo && response.usersBasicInfo.length > 0) {
        const newOptions = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        
        // Prevent duplicate options
        const existingIds = new Set(userOptions.map(opt => opt.value));
        const uniqueNewOptions = newOptions.filter(opt => !existingIds.has(opt.value));
        
        if (uniqueNewOptions.length > 0) {
          setUserOptions(prev => [...prev, ...uniqueNewOptions]);
          setHasMoreUsers(response.totalRecord > (userPage + 1) * 10);
          setUserPage(prev => prev + 1);
        } else {
          setHasMoreUsers(false);
        }
      } else {
        setHasMoreUsers(false);
      }
    } catch (error) {
      console.error('Error loading more users:', error);
      toast.error('Error loading more users');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Function to load designations for search
  const loadDesignations = async (inputValue, callback) => {
    if (!inputValue) {
      callback(designationOptions);
      return;
    }

    try {
      setIsLoadingDesignations(true);
      const response = await fetchDesignationsWithSearch(0, 10, inputValue);
      
      if (response.designations) {
        const options = response.designations.map(d => ({
          value: d.designationID,
          label: d.designationName
        }));
        setDesignationOptions(options);
        setDesignationPage(1);
        setHasMoreDesignations(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading designations:', error);
      toast.error('Error loading designations');
      callback([]);
    } finally {
      setIsLoadingDesignations(false);
    }
  };

  // Function to load departments for search
  const loadDepartments = async (inputValue, callback) => {
    if (!inputValue) {
      callback(departmentOptions);
      return;
    }

    try {
      setIsLoadingDepartments(true);
      const response = await fetchDepartmentsWithSearch(0, 10, inputValue);
      
      if (response.departments) {
        const options = response.departments.map(d => ({
          value: d.departmentID,
          label: d.departmentName
        }));
        setDepartmentOptions(options);
        setDepartmentPage(1);
        setHasMoreDepartments(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading departments:', error);
      toast.error('Error loading departments');
      callback([]);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Function to load roles for search
  const loadRoles = async (inputValue, callback) => {
    if (!inputValue) {
      callback(roleOptions);
      return;
    }

    try {
      setIsLoadingRoles(true);
      const response = await fetchRolesWithSearch(0, 10, inputValue);
      
      if (response.roles) {
        const options = response.roles.map(r => ({
          value: r.roleID,
          label: r.roleName
        }));
        setRoleOptions(options);
        setRolePage(1);
        setHasMoreRoles(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading roles:', error);
      toast.error('Error loading roles');
      callback([]);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  // Function to load users for search
  const loadUsers = async (inputValue, callback) => {
    if (!inputValue) {
      callback(userOptions);
      return;
    }

    try {
      setIsLoadingUsers(true);
      const response = await fetchUsersBasicInfo(0, 10, inputValue);
      
      if (response.usersBasicInfo) {
        const options = response.usersBasicInfo.map(u => ({
          value: u.userID.toString(),
          label: `${u.firstName} ${u.lastName} (${u.employeeID})`
        }));
        setUserOptions(options);
        setUserPage(1);
        setHasMoreUsers(response.totalRecord > 10);
        callback(options);
      } else {
        callback([]);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Error loading users');
      callback([]);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [departmentsRes, usersRes, rolesRes] = await Promise.all([
          fetchAllDepartments(),
          fetchUsersBasicInfo(1, 1000),
          fetchAllRoles()
        ]);

        if (departmentsRes.departments) {
          setDepartments(departmentsRes.departments.map((d) => ({ value: d.departmentID, label: d.departmentName })));
        }

        if (usersRes.usersBasicInfo) {
          setUsers(usersRes.usersBasicInfo.map((u) => ({ 
            value: u.userID.toString(), 
            label: `${u.firstName} ${u.lastName} (${u.employeeID})`
          })));
        }

        if (rolesRes.roles) {
          setRoles(rolesRes.roles.map(role => ({ value: role.roleID.toString(), label: role.roleName })));
        }
      } catch (error) {
        console.error('Error fetching dropdown data:', error);
        toast.error('Error fetching form data. Please refresh the page.');
      }
    };

    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const requiredFields = [
      'FirstName', 'LastName', 'Gender', 'EmployeeID', 'RoleID',
      'DepartmentID', 'DesignationID', 'EmailID', 'LoginID', 'Password', 'ConfirmPassword'
    ];

    for (let field of requiredFields) {
      if (!formData[field]) {
        toast.error(`Please fill in the ${field} field.`);
        return;
      }
    }

    if (formData.Password !== formData.ConfirmPassword) {
      toast.error('Passwords do not match.');
      return;
    }

    // Validate gender
    if (!['M', 'F', 'O'].includes(formData.Gender)) {
      toast.error('Please select a valid gender');
      return;
    }

    try {
      const payload = {
        employeeID: formData.EmployeeID.trim(),
        firstName: formData.FirstName.trim(),
        lastName: formData.LastName.trim(),
        gender: formData.Gender,
        categoryType: formData.CategoryType || '',
        roleID: Number(formData.RoleID),
        departmentID: Number(formData.DepartmentID),
        designationID: Number(formData.DesignationID),
        reportsTo: formData.ReportsTo ? Number(formData.ReportsTo) : 0,
        emailID: formData.EmailID.trim(),
        loginID: formData.LoginID.trim(),
        password: formData.Password,
        inductionRequire: formData.InductionRequire || false,
        userProfileID: Number(formData.UserProfileID) || 0,
        createdBy: 'admin',
        reasonForChange: 'Initial creation',
        electronicSignature: 'admin-signature',
        signatureDate: new Date().toISOString(),
        plantID: Number(sessionStorage.getItem('plantId')) || 0
      };

      console.log("Sending payload:", payload);
      const response = await createUserBasicInfo(payload);
      console.log("Create response:", response);

      if (response.header?.errorCount === 0) {
        const allMessages = response.header?.messages || [];
        const errorMessages = allMessages.filter(msg => msg.messageLevel === 'Error');
        const infoMessages = allMessages.filter(msg => msg.messageLevel === 'Information');

        if (errorMessages.length > 0) {
          errorMessages.forEach(msg => toast.error(msg.messageText));
        } else {
          infoMessages.forEach((msg, index) => {
            setTimeout(() => {
              toast.success(msg.messageText);
            }, index * 500);
          });

          // Delay navigation slightly more than the total toast duration
          setTimeout(() => {
            navigate('/system-admin/user-master');
          }, infoMessages.length * 500 + 3200);
        }
      } else {
        const allMessages = response.header?.messages || [];
        const errorMessages = allMessages.filter(msg => msg.messageLevel === 'Error');
        errorMessages.forEach(msg => toast.error(msg.messageText));
      }
    } catch (error) {
      console.error('Error creating user:', error);
      const errorMsg =
        error?.response?.data?.messages?.[0]?.messageText ||
        error?.response?.data?.message ||
        'An error occurred while creating user.';
      toast.error(errorMsg);
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Add User Master</h3>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>First Name <span className={styles.required}>*</span></label>
                <input type="text" name="FirstName" value={formData.FirstName} onChange={handleChange} required />
              </div>
              <div className={styles.row}>
                <label>Last Name <span className={styles.required}>*</span></label>
                <input type="text" name="LastName" value={formData.LastName} onChange={handleChange} required />
              </div>
            </div>

            <div className={styles.row}>
              <label>Gender <span className={styles.required}>*</span></label>
              <Select
                name="Gender"
                options={[
                  { value: 'M', label: 'Male' },
                  { value: 'F', label: 'Female' },
                  { value: 'O', label: 'Other' }
                ]}
                value={
                  ['M', 'F', 'O'].includes(formData.Gender)
                    ? { value: formData.Gender, label: { M: 'Male', F: 'Female', O: 'Other' }[formData.Gender] }
                    : null
                }
                onChange={(selected) =>
                  setFormData({ ...formData, Gender: selected ? selected.value : '' })
                }
                isClearable
                placeholder="-- Select Gender --"
                className={styles.reactSelect}
              />
            </div>

            <div className={styles.row}>
              <label>Employee ID <span className={styles.required}>*</span></label>
              <input type="text" name="EmployeeID" value={formData.EmployeeID} onChange={handleChange} required />
            </div>

            <div className={styles.row}>
              <label>Category Type</label>
              <input type="text" name="CategoryType" value={formData.CategoryType} onChange={handleChange} />
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Role <span className={styles.required}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={roleOptions}
                  value={roleOptions.find((r) => r.value.toString() === formData.RoleID)}
                  loadOptions={(inputValue, callback) => loadRoles(inputValue, callback)}
                  onChange={(selected) => setFormData({ ...formData, RoleID: selected ? selected.value : '' })}
                  onMenuOpen={handleRoleMenuOpen}
                  onMenuScrollToBottom={loadMoreRoles}
                  onInputChange={(value) => {
                    setRoleSearch(value);
                    if (!value) {
                      setRolePage(0);
                      loadInitialRoles();
                    } else {
                      setRolePage(0);
                      setRoleOptions([]);
                      setHasMoreRoles(true);
                    }
                  }}
                  isLoading={isLoadingRoles}
                  placeholder="-- Select Role --"
                  className={styles.reactSelect}
                  noOptionsMessage={() => roleOptions.length === 0 ? "No roles found" : ""}
                  styles={customStyles}
                  components={{
                    LoadingMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>Loading...</div>,
                    NoOptionsMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>
                      {roleOptions.length === 0 ? "No roles found" : "Loading more..."}
                    </div>
                  }}
                  menuPortalTarget={document.body}
                />
              </div>

              <div className={styles.row}>
                <label>Department <span className={styles.required}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={departmentOptions}
                  value={departmentOptions.find((d) => d.value.toString() === formData.DepartmentID)}
                  loadOptions={(inputValue, callback) => loadDepartments(inputValue, callback)}
                  onChange={(selected) => setFormData({ ...formData, DepartmentID: selected ? selected.value : '' })}
                  onMenuOpen={handleDepartmentMenuOpen}
                  onMenuScrollToBottom={loadMoreDepartments}
                  onInputChange={(value) => {
                    setDepartmentSearch(value);
                    if (!value) {
                      setDepartmentPage(0);
                      loadInitialDepartments();
                    } else {
                      setDepartmentPage(0);
                      setDepartmentOptions([]);
                      setHasMoreDepartments(true);
                    }
                  }}
                  isLoading={isLoadingDepartments}
                  placeholder="-- Select Department --"
                  className={styles.reactSelect}
                  noOptionsMessage={() => departmentOptions.length === 0 ? "No departments found" : ""}
                  styles={customStyles}
                  components={{
                    LoadingMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>Loading...</div>,
                    NoOptionsMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>
                      {departmentOptions.length === 0 ? "No departments found" : "Loading more..."}
                    </div>
                  }}
                  menuPortalTarget={document.body}
                />
              </div>
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Designation <span className={styles.required}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={designationOptions}
                  value={designationOptions.find((d) => d.value.toString() === formData.DesignationID)}
                  loadOptions={(inputValue, callback) => loadDesignations(inputValue, callback)}
                  onChange={(selected) => setFormData({ ...formData, DesignationID: selected ? selected.value : '' })}
                  onMenuOpen={handleMenuOpen}
                  onMenuScrollToBottom={loadMoreDesignations}
                  onInputChange={(value) => {
                    setDesignationSearch(value);
                    if (!value) {
                      setDesignationPage(0);
                      loadInitialDesignations();
                    } else {
                      setDesignationPage(0);
                      setDesignationOptions([]);
                      setHasMoreDesignations(true);
                    }
                  }}
                  isLoading={isLoadingDesignations}
                  placeholder="-- Select Designation --"
                  className={styles.reactSelect}
                  noOptionsMessage={() => designationOptions.length === 0 ? "No designations found" : ""}
                  styles={customStyles}
                  components={{
                    LoadingMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>Loading...</div>,
                    NoOptionsMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>
                      {designationOptions.length === 0 ? "No designations found" : "Loading more..."}
                    </div>
                  }}
                  menuPortalTarget={document.body}
                />
              </div>

              <div className={styles.row}>
                <label>Reports To <span className={styles.required}>*</span></label>
                <AsyncSelect
                  cacheOptions
                  defaultOptions={userOptions}
                  value={userOptions.find((u) => u.value === formData.ReportsTo)}
                  loadOptions={(inputValue, callback) => loadUsers(inputValue, callback)}
                  onChange={(selected) => setFormData({ ...formData, ReportsTo: selected ? selected.value : '' })}
                  onMenuOpen={handleUserMenuOpen}
                  onMenuScrollToBottom={loadMoreUsers}
                  onInputChange={(value) => {
                    setUserSearch(value);
                    if (!value) {
                      setUserPage(0);
                      loadInitialUsers();
                    } else {
                      setUserPage(0);
                      setUserOptions([]);
                      setHasMoreUsers(true);
                    }
                  }}
                  isLoading={isLoadingUsers}
                  placeholder="-- Select Manager --"
                  className={styles.reactSelect}
                  noOptionsMessage={() => userOptions.length === 0 ? "No users found" : ""}
                  styles={customStyles}
                  components={{
                    LoadingMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>Loading...</div>,
                    NoOptionsMessage: () => <div style={{ padding: '8px', textAlign: 'center' }}>
                      {userOptions.length === 0 ? "No users found" : "Loading more..."}
                    </div>
                  }}
                  menuPortalTarget={document.body}
                />
              </div>
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Email <span className={styles.required}>*</span></label>
                <input 
                  type="email" 
                  name="EmailID" 
                  value={formData.EmailID} 
                  onChange={handleChange}
                  pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                  title="Please enter a valid email address"
                  required 
                />
              </div>
              <div className={styles.row}>
                <label>Login ID <span className={styles.required}>*</span></label>
                <input type="text" name="LoginID" value={formData.LoginID} onChange={handleChange} required />
              </div>
            </div>

            <div className={styles.inlineRow}>
              <div className={styles.row}>
                <label>Password <span className={styles.required}>*</span></label>
                <div className={styles.passwordWrapper}>
                  <input
                    type={showPassword ? "text" : "password"}
                    name="Password"
                    value={formData.Password}
                    onChange={handleChange}
                    required
                    minLength={8}
                  />
                  <span
                    className={styles.eyeIcon}
                    onClick={() => setShowPassword((prev) => !prev)}
                    style={{ cursor: 'pointer' }}
                  >
                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>
              </div>

              <div className={styles.row}>
                <label>Confirm Password <span className={styles.required}>*</span></label>
                <div className={styles.passwordWrapper}>
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    name="ConfirmPassword"
                    value={formData.ConfirmPassword}
                    onChange={handleChange}
                    required
                    className={formData.Password && formData.ConfirmPassword && formData.Password !== formData.ConfirmPassword ? styles.errorInput : ''}
                  />
                  <span
                    className={styles.eyeIcon}
                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                    style={{ cursor: 'pointer' }}
                  >
                    {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                  </span>
                </div>
              </div>
            </div>

            <div className={styles.submitRow}>
              <button type="submit" className={styles.primaryBtn}>Submit</button>
              <button type="button" onClick={() => navigate(-1)} className={styles.cancelBtn}>Cancel</button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default AddUser;