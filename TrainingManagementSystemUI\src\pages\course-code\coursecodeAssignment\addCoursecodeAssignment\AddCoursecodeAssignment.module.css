.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 2.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: #333;
  box-sizing: border-box;
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.required {
  color: red;
  margin-left: 4px;
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

/* React Select Styles */
.reactSelect :global(.react-select__control) {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 2px 5px;
  font-size: 14px;
  color: black;
  min-height: 38px;
}

.reactSelect :global(.react-select__menu) {
  color: #001b36;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.reactSelect :global(.react-select__option) {
  color: #001b36;
  font-size: 14px;
  padding: 8px 12px;
}

.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

/* User Selection Panel Styles */
.userSelectionPanel {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  height: 500px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
}

.userGridColumn {
  flex: 0 0 70%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
  overflow: hidden;
}

/* Divider between columns */
.userSelectionDivider {
  width: 1px;
  background-color: #dee2e6;
  flex-shrink: 0;
}

.selectedUsersColumn {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.userGridColumn h4,
.selectedUsersColumn h4 {
  margin: 0;
  color: #127C96;
  font-size: 1rem;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  text-align: center;
  flex-shrink: 0;
}

.searchFilters {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.searchInput {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

.userGrid {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
  padding: 0.5rem;
}

.userGridHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 80px;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.userGridBody {
  flex: 1;
  overflow-y: auto;
}

.userGridRow {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 80px;
  gap: 1rem;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.userGridRow:hover {
  background-color: #f8f9fa;
}

.addUserBtn {
  background: none;
  border: none;
  color: #28a745;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.2rem;
}

.addUserBtn:hover {
  color: #218838;
}

.noUsers {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
  font-size: 0.9rem;
}

.loadingIndicator {
  text-align: center;
  padding: 1rem;
  color: #6c757d;
  font-size: 0.9rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #e9ecef;
}

.selectedUsersList {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
}

.selectedUserItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.selectedUserItem:last-child {
  margin-bottom: 0;
}

.userInfo {
  flex: 1;
}

.userName {
  font-weight: 600;
  color: #127C96;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.userDepartment {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.userDesignation {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.userRole {
  font-size: 0.8rem;
  color: #495057;
}

.removeUserBtn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-weight: bold;
  font-size: 1rem;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 0.2rem;
}

.removeUserBtn:hover {
  color: #c82333;
}

.noSelectedUsers {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
  font-size: 0.9rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }

  .form {
    padding: 1rem 0.8rem;
    margin: 0;
    max-width: 100%;
    border-radius: 0;
    gap: 1rem;
  }

  .userSelectionPanel {
    flex-direction: column;
    height: auto;
    min-height: 400px;
  }

  .userGridColumn,
  .selectedUsersColumn {
    flex: none;
    height: auto;
    min-height: 200px;
  }

  .searchFilters {
    flex-direction: column;
    gap: 0.5rem;
  }

  .userGridHeader,
  .userGridRow {
    grid-template-columns: 1fr 1fr 1fr 60px;
    gap: 0.5rem;
    font-size: 0.8rem;
  }

  .userGridHeader div:nth-child(4),
  .userGridRow div:nth-child(4) {
    display: none; /* Hide role column on mobile */
  }

  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.25rem;
  }

  .form {
    padding: 15px;
  }

  .userGridHeader,
  .userGridRow {
    grid-template-columns: 1fr 1fr 60px;
    gap: 0.5rem;
  }

  .userGridHeader div:nth-child(3),
  .userGridRow div:nth-child(3) {
    display: none; /* Hide designation column on small mobile */
  }

  .selectedUserItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .removeUserBtn {
    align-self: flex-end;
  }
}
