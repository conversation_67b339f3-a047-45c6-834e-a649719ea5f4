.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.passwordReset {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

  .resetInput {
    padding: 6px 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 14px;
    width: 160px;
  }

  .resetBtn {
    background-color: #127C96;
    color: white;
    padding: 8px 14px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.controls input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
  width: 200px;
}

.filterBtn {
  background-color: #2c3e50;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
}

.addUserBtn {
  background-color: #127C96;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure the modal is on top */
}

.modalContent {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  box-sizing: border-box; /* Ensure padding doesn't overflow */
  color: #2c3e50;
}

.modalActions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.confirmBtn, .cancelBtn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.confirmBtn {
  background-color: #127C96;
  color: white;
}

.cancelBtn {
  background-color: #f26d6d;
  color: white;
}

.userTableContainer {
  margin-top: 20px;
  flex-grow: 1; /* Allow the table container to grow and fill the available space */
  height: 70%; /* Set the height to 70% of its parent container */
  overflow: hidden; /* Disable vertical scrolling within the table container */
}


.userTable {
  width: 100%;
  border-collapse: collapse; /* Merge borders */
  table-layout: fixed; /* Ensure consistent column widths */
}

.userTable th,
.userTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0; /* Line at the bottom of each row */
  vertical-align: middle; /* Align content vertically in the middle */
  text-align: left;
  box-sizing: border-box; /* Ensure padding doesn't overflow */
  height: 70px; /* Ensure consistent height for all cells */
}

.userInfo {
  display: flex;
  align-items: center; /* Align the avatar and text vertically */
  gap: 10px; /* Space between the avatar and name */
  height: 100%; /* Ensure the height of the cell is consistent */
}

.profile {
  width: 20px;
  height: 20px;
  object-fit: cover; /* Ensure the image fits within the container */
  vertical-align: middle; /* Align the image vertically within the cell */
}

/* Optional: Ensure badges are aligned properly */
.badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  color: white;
  text-transform: capitalize;
  box-sizing: border-box;
}

/* Department Badges */
.dept1 {
  color: #000000;
  /* background-color: #f26d6d; */
}

.dept2 {
  color: #000000;

  /* background-color: #9c88ff; */
}

.dept3 {
  color: #000000;

  /* background-color: #6dcff6; */
}

/* Role Badges */
.admin {
  color: #000000;

  /* background-color: #ff7675; */
}

.user {
  /* background-color: #55efc4; */
  color: #2d3436;
}

.mentor {
  /* background-color: #ffeaa7; */
  color: #2d3436;
}

.actions {
  display: flex;
  gap: 12px;
  justify-content: flex-start; /* Move buttons to the left */
  align-items: center;
}

.actions .editIcon,
.actions .deleteIcon {
  font-size: clamp(20px, 2vw, 24px); /* Responsive with a safe min and max */
  min-width: 24px;
  min-height: 24px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
}


.editBtn {
  background-color: transparent; /* or any color you want */
  border: none;
  padding: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actions svg {
  font-size: 25px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
}

.actions svg:hover {
  transform: scale(1.2);
}

.actions .editIcon {
  color: #127C96; /* Bootstrap-like primary blue */
}

.actions .editIcon:active {
  transform: scale(0.95);
  color: #127C96  ; /* darker blue on click */
}

.actions .deleteIcon {
  color: #4d91a1  ; /* standard red */
}

.actions .deleteIcon:active {
  transform: scale(0.95);
  color: #65aec0  ; /* darker red on click */
}

.pagination {
  margin-bottom: 5rem;
  display: flex;
  justify-content: flex-end;
  gap: 6px;
  flex-wrap: wrap; /* Ensure the buttons wrap to the next line if they overflow */
}

.pagination button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: white;
  cursor: pointer;
  color: black;
  font-size: 14px;
  white-space: nowrap; /* Prevent text from wrapping inside buttons */
}

.pagination .active {
  background-color: #127C96  ;
  color: white;
  font-weight: bold;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f0f0f0; /* or something visible */
  border-radius: 4px;
  color: #333;
  box-sizing: border-box;
}
.resetBtn {
    background-color: transparent !important;
    border: none !important;
    color: inherit !important;
    padding: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    align-items: center;
  }

  .actions .resetIcon   {
    font-size: clamp(20px, 2vw, 24px);
    cursor: pointer;
    transition: transform 0.2s ease, color 0.2s ease;
  }
  .editBtn:hover {
    color: #127C96;
  }
@media (max-width: 768px) { /* Tablet size and below */
  .actions .resetIcon
  {
    font-size: 24px; /* Keep the size same for tablet */
  }
}

@media (max-width: 480px) { /* Mobile size */
  .actions .editIcon,
  .actions .deleteIcon {
    font-size: 24px; /* Keep the size same for mobile */
  }
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #127C96;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 50px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
