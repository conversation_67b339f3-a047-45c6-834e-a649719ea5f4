import React, { useState, useEffect, useContext } from 'react';
import styles from './DesignationMaster.module.css';

import { FaEdit } from 'react-icons/fa';
import { FcCancel } from 'react-icons/fc';
import { useNavigate } from 'react-router-dom';
import { DesignationContext } from '../../../context/DeignationContext';
import { fetchAllDesignations, deleteDesignation } from '../../../services/systemAdmin/DesignationMasterService';
import Pagination from '../../../components/pagination/Pagination';
import Modal from '../../../components/common/Modal';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const DesignationMaster = () => {
  const navigate = useNavigate();
  const [designations, setDesignations] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const itemsPerPage = 10;
  const { setDesignationDetails } = useContext(DesignationContext);
  const [showModal, setShowModal] = useState(false);
  const [designationToDelete, setDesignationToDelete] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Debounce searchTerm
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // reset to first page on search
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    const loadDesignations = async () => {
      setIsLoading(true);
      try {
        const data = await fetchAllDesignations(currentPage, itemsPerPage, debouncedSearchTerm);
        console.log('Designations Data:', data);

        const message = data.header?.messages?.[0];
        if (message?.messageLevel?.toLowerCase() === 'warning') {
          toast.warning(message.messageText);
        } else if (message?.messageLevel?.toLowerCase() === 'error') {
          toast.error(message.messageText);
        }

        if (data.header?.errorCount === 0 && Array.isArray(data.designations)) {
          setDesignations(data.designations);
          setTotalRecords(data.totalRecord);
        } else {
          console.error('Failed to load designations:', data.header?.message);
        }
      } catch (error) {
        toast.error('Error fetching designations');
        console.error('Error fetching designations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDesignations();
  }, [currentPage, debouncedSearchTerm]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const navigateTo = (path, state = {}) => {
    navigate(path, { state });
  };

  const handleEditDesignation = (designation) => {
    setDesignationDetails(designation.designationID, designation.designationName);
    navigateTo('/system-admin/designation-master/edit-designation', { designationData: designation });
  };

  const handleDeleteDesignation = (designation) => {
    setDesignationToDelete(designation);
    setShowModal(true);
  };

  const confirmDeleteDesignation = async () => {
    try {
      const res = await deleteDesignation(designationToDelete.designationID);
      if (res.header?.errorCount === 0) {
        toast.success('Designation deleted successfully');

        // Reload after deletion
        const data = await fetchAllDesignations(currentPage, itemsPerPage, debouncedSearchTerm);
        setDesignations(data.designations);
        setTotalRecords(data.totalRecord);
      } else {
        toast.error(res.header?.messages?.[0]?.messageText || 'Failed to delete designation');
      }
    } catch (err) {
      toast.error('Error deleting designation');
      console.error('Delete Error:', err);
    } finally {
      setShowModal(false);
      setDesignationToDelete(null);
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      
      <div className={styles.container}>
        <div className={styles.userMaster}>
          <div className={styles.panelHeader}>
            <h2>Designation Master</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
              <button
                className={styles.addUserBtn}
                onClick={() => navigateTo('/system-admin/designation-master/add-designation')}
              >
                + Add
              </button>
            </div>
          </div>

          <div className={styles.userTableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th>Designation Name</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan="2" style={{ textAlign: "center", padding: "20px" }}>
                      <div className={styles.loader}></div>
                    </td>
                  </tr>
                ) : designations.length > 0 ? (
                  designations.map((item, index) => (
                    <tr key={index}>
                      <td>{item.designationName}</td>
                      <td>
                        <div className={styles.actions}>
                          <button 
                            className={styles.editBtn} 
                            onClick={() => handleEditDesignation(item)}
                            title="Edit Designation"
                          >
                            <FaEdit className={styles.editIcon} />
                          </button>
                          <button 
                            className={styles.deleteBtn} 
                            onClick={() => handleDeleteDesignation(item)}
                            title="Delete Designation"
                          >
                            <FcCancel className={styles.deleteIcon} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="2" style={{ textAlign: 'center', padding: '20px' }}>
                      No designations found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>

      {showModal && designationToDelete && (
        <Modal
          title="Confirm Delete"
          message={`Are you sure you want to delete "${designationToDelete.designationName}"?`}
          onConfirm={confirmDeleteDesignation}
          onCancel={() => setShowModal(false)}
        />
      )}
    </>
  );
};

export default DesignationMaster;
