import React, { useState, useEffect, useRef } from 'react';
import { FaCheck, FaTimes, FaEye, FaUndo } from 'react-icons/fa'; // Removed FaDownload
import styles from './coursecodeApprove.module.css'; // Updated CSS import
import Pagination from '../../../components/pagination/Pagination';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Modal from '../../../components/common/Modal';
import { fetchCourseCodesByUserId, updateCourseCodeStatus, getCourseCodeById } from '../../../services/course-code/CoursecodeService';
import CoursecodeView from '../../../components/common/coursecode/CoursecodeView';

const CoursecodeApprove = () => { // Updated component name
  const [searchTerm, setSearchTerm] = useState('');
  const [coursecodeData, setCoursecodeData] = useState([]); // Updated state name
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [selectedCoursecode, setSelectedCoursecode] = useState(null); // Updated state name
  const [approvalRemarks, setApprovalRemarks] = useState('');
  const [rejectionRemarks, setRejectionRemarks] = useState('');
  const [returnRemarks, setReturnRemarks] = useState('');
  const tableContainerRef = useRef(null);
  const itemsPerPage = 10;
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewCourseCodeDetails, setViewCourseCodeDetails] = useState(null);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const userID = sessionStorage.getItem('userID');
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: debouncedSearchTerm,
          showOnlyUnderReview: true
        };
        // Updated API call
        const res = await fetchCourseCodesByUserId(payload);
        // Assuming response structure is similar to OJTApproval, adjust if needed
        setCoursecodeData(res.coursecodes || []); // Adjust key based on actual API response
        setTotalRecords(res.totalRecord || 0);
      } catch {
        setCoursecodeData([]); // Updated state name
        setTotalRecords(0);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [debouncedSearchTerm, currentPage]);

  const checkScroll = () => {
    // No-op: removed showLeftIndicator and showRightIndicator
  };

  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener('scroll', checkScroll);
      window.addEventListener('resize', checkScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScroll);
        window.removeEventListener('resize', checkScroll);
      }
    };
  }, []);

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1);
  };

  const handleApprove = (coursecode) => {
    console.log('Approve clicked', coursecode);
    setSelectedCoursecode(coursecode);
    setShowApproveModal(true);
  };

  const handleReject = (coursecode) => { // Updated parameter name
    setSelectedCoursecode(coursecode); // Updated state name
    setShowRejectModal(true);
  };

  const handleReturn = (coursecode) => { // Updated parameter name
    setSelectedCoursecode(coursecode); // Updated state name
    setShowReturnModal(true);
  };

  const handleView = async (coursecode) => {
    setLoading(true);
    try {
      const res = await getCourseCodeById(coursecode.courseCodeID);
      setViewCourseCodeDetails(res.coursecode);
      setShowViewModal(true);
    } catch {
      toast.error('Failed to fetch course code details');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (status, remarks = '') => {
    if (!remarks.trim()) {
      toast.error(`Please provide ${status.toLowerCase()} remarks`);
      return;
    }
    let closeModal = null;
    let clearRemarks = null;
    if (status === 'Rejected') {
      closeModal = setShowRejectModal;
      clearRemarks = setRejectionRemarks;
    } else if (status === 'Returned') {
      closeModal = setShowReturnModal;
      clearRemarks = setReturnRemarks;
    } else {
      closeModal = setShowApproveModal;
      clearRemarks = setApprovalRemarks;
    }
    try {
      const userID = sessionStorage.getItem('userID');
      const res = await updateCourseCodeStatus({
        courseCodeID: selectedCoursecode.courseCodeID,
        courseCodeStatus: status,
        modifiedBy: userID,
        reasonForChange: remarks
      });
      if (res.header && res.header.errorCount === 0) {
        toast.success(res.header.messages?.[0]?.messageText || `Course Code ${status.toLowerCase()}d successfully`);
        // Refresh list
        const payload = {
          userID: userID ? parseInt(userID) : 0,
          page: {
            offset: (currentPage - 1) * itemsPerPage,
            fetch: itemsPerPage
          },
          searchText: searchTerm,
          showOnlyUnderReview: true
        };
        const fetchRes = await fetchCourseCodesByUserId(payload);
        setCoursecodeData(fetchRes.coursecodes || []);
        setTotalRecords(fetchRes.totalRecord || 0);
        // Close modal and clear remarks after success
        closeModal(false);
        clearRemarks('');
        setSelectedCoursecode(null);
      } else {
        toast.error(res.header?.messages?.[0]?.messageText || `Failed to ${status.toLowerCase()} Course Code`);
      }
    } catch {
      toast.error(`Failed to ${status.toLowerCase()} Course Code`);
    }
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      {showApproveModal && (
        <Modal
          title="Confirm Approval"
          message={
            <div>
              <p>Are you sure you want to approve the course code "{selectedCoursecode?.courseTitle}" ({selectedCoursecode?.courseCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="approveReason">Reason for approval:</label>
                <textarea
                  id="approveReason"
                  value={approvalRemarks}
                  onChange={(e) => setApprovalRemarks(e.target.value)}
                  placeholder="Please provide a reason for approval"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Approved', approvalRemarks)}
          onCancel={() => {
            setShowApproveModal(false);
            setSelectedCoursecode(null);
            setApprovalRemarks('');
          }}
        />
      )}

      {showRejectModal && (
        <Modal
          title="Confirm Rejection"
          message={
            <div>
              <p>Are you sure you want to reject the course code "{selectedCoursecode?.courseTitle}" ({selectedCoursecode?.courseCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="rejectReason">Reason for rejection:</label>
                <textarea
                  id="rejectReason"
                  value={rejectionRemarks}
                  onChange={(e) => setRejectionRemarks(e.target.value)}
                  placeholder="Please provide a reason for rejection"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Rejected', rejectionRemarks)}
          onCancel={() => {
            setShowRejectModal(false);
            setSelectedCoursecode(null);
            setRejectionRemarks('');
          }}
        />
      )}

      {showReturnModal && (
        <Modal
          title="Confirm Return"
          message={
            <div>
              <p>Are you sure you want to return the course code "{selectedCoursecode?.courseTitle}" ({selectedCoursecode?.courseCode})?</p>
              <div className={styles.reasonInput}>
                <br />
                <label htmlFor="returnReason">Reason for return:</label>
                <textarea
                  id="returnReason"
                  value={returnRemarks}
                  onChange={(e) => setReturnRemarks(e.target.value)}
                  placeholder="Please provide a reason for return"
                  required
                />
              </div>
            </div>
          }
          onConfirm={() => handleStatusUpdate('Returned', returnRemarks)}
          onCancel={() => {
            setShowReturnModal(false);
            setSelectedCoursecode(null);
            setReturnRemarks('');
          }}
        />
      )}

      {showViewModal && (
        <CoursecodeView 
          courseCodeDetails={viewCourseCodeDetails}
          onClose={() => setShowViewModal(false)}
        />
      )}

      <div className={styles.container}>
        <div className={styles.documentReview}>
          <div className={styles.panelHeader}>
            <h2>Course Code Approval</h2> {/* Updated title */}
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearch}
                className={styles.searchInput}
              />
            </div>
          </div>

          <div ref={tableContainerRef} className={styles.docTableContainer}>
            <div className={styles.tableWrapper}>
              <table className={styles.docTable}>
                <thead>
                  <tr>
                    <th>Title</th>
                    <th>Course Code</th>
                    <th>Department</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="4" className={styles.spinnerCell}>
                        <div className={styles.spinner}></div>
                      </td>
                    </tr>
                  ) : coursecodeData.length === 0 ? (
                    <tr>
                      <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                        No Course Code records found.
                      </td>
                    </tr>
                  ) : (
                    coursecodeData.map((item) => (
                      <tr key={item.courseCodeID}>
                        <td>{item.courseTitle}</td>
                        <td>{item.courseCode}</td>
                        <td>{item.departmentName}</td>
                        <td>
                          <div className={styles.actions}>
                            {/* View Button */}
                            <button
                              className={styles.actionButton}
                              onClick={() => handleView(item)}
                              title="View"
                            >
                              <FaEye className={styles.viewIcon} />
                              <span>View</span>
                            </button>
                            {/* Divider */}
                            <div className={styles.actionDivider}></div>
                            {/* Approve Button */}
                            <button
                              className={styles.actionButton}
                              onClick={() => handleApprove(item)}
                              title="Approve"
                            >
                              <FaCheck className={styles.approveIcon} />
                              <span>Approve</span>
                            </button>
                             {/* Return Button */}
                             <button
                              className={styles.actionButton}
                              onClick={() => handleReturn(item)}
                              title="Return"
                            >
                              <FaUndo className={styles.returnIcon} />
                              <span>Return</span>
                            </button>
                            {/* Reject Button */}
                            <button
                              className={styles.actionButton}
                              onClick={() => handleReject(item)}
                              title="Reject"
                            >
                              <FaTimes className={styles.rejectIcon} />
                              <span>Reject</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default CoursecodeApprove; // Updated component name
