.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.form {
  max-width: calc(100% - 2rem);
  margin: 0 auto;
  background: #fff;
  padding: 1.5rem 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  color: #333;
  box-sizing: border-box;
}

.formContent {
  display: flex;
  flex-direction: column;
}

.sectionHeading {
  font-size: 22px;
  font-weight: 600;
  color: #00376e;
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.row {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.row label {
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

.required {
  color: red;
  margin-left: 4px;
}

.row input,
.row select,
.row textarea {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  width: 100%;
  color: black;
}

.row input::placeholder,
.row select::placeholder,
.row textarea::placeholder {
  color: #a9a9a9;
  font-size: 10px;
}

.row textarea {
  resize: vertical;
  min-height: 80px;
}

/* Styling for the dynamic Activity Details section */
.activityDetailsContainer {
  margin-top: 2rem;
  border-top: 1px solid #e0e0e0;
  padding-top: 1.5rem;
}

.fileDetails {
  margin-top: 0.8rem;
  padding: 0.8rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  font-size: 0.9rem;
  color: #333;
}

.fileDetails p {
  margin: 0 0 0.4rem 0;
  padding: 0;
}

.fileDetails p:last-child {
  margin-bottom: 0;
}

.submitRow {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 2.5rem;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #127c96;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #0f6a83;
}

.primaryBtn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.cancelBtn {
  background-color: #e0e0e0;
  color: #333;
  border: 1px solid #999;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #cccccc;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form {
    padding: 1rem 0.8rem;
    margin: 0;
    max-width: 100%;
    border-radius: 0;
    gap: 1rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .form {
    padding: 15px;
  }

  .submitRow {
    flex-direction: column;
    gap: 10px;
  }

  .primaryBtn,
  .cancelBtn {
    width: 100%;
  }
}

.removeActivityDetailButton {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 0.4rem 0.8rem;
  cursor: pointer;
  flex-shrink: 0;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.removeActivityDetailButton:hover {
  background-color: #d32f2f;
}

.durationInput {
  width: 60px !important;
  padding: 8px !important;
  font-size: 0.9rem !important;
  text-align: center;
  border: 1px solid #ccc !important;
  border-radius: 8px !important;
}

.durationInput::placeholder {
  font-size: 0.8rem;
  color: #999;
}

.durationLabel {
  font-size: 0.9rem;
  color: #666;
  margin: 0 4px;
}
