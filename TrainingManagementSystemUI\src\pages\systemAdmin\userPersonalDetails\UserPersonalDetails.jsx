import React, { useState, useEffect } from 'react';
import styles from './UserPersonalDetails.module.css';
import avatar from '../../../assets/images/profile.png';

import { FaEdit } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { fetchAllUserPersonalDetails } from '../../../services/systemAdmin/UserPersonalDetailsService';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Pagination from '../../../components/pagination/Pagination';

const UserPersonalDetails = () => {
  const navigate = useNavigate();
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [totalRecords, setTotalRecords] = useState(0);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on new search
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    const loadUsers = async () => {
      try {
        setLoading(true);
        const data = await fetchAllUserPersonalDetails(currentPage, itemsPerPage, debouncedSearchTerm);

        const message = data.header?.messages?.[0];
        if (message?.messageLevel?.toLowerCase() === 'warning') {
          toast.warning(message.messageText);
        } else if (message?.messageLevel?.toLowerCase() === 'error') {
          toast.error(message.messageText);
        }

        if (data.header?.errorCount === 0 && Array.isArray(data.usersPersonalnfo)) {
          setUsers(data.usersPersonalnfo);
          setTotalRecords(data.totalRecord || 0);
        } else {
          console.error('Failed to load users:', data.header?.message);
        }
      } catch (error) {
        toast.error('Error fetching users');
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [currentPage, debouncedSearchTerm]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleEditUserClick = (user) => {
    navigate('/system-admin/User-Personal-Details/edit-user', { state: { userData: user } });
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleDateString();
  };

  const getUserStatus = (user) => {
    const requiredFields = {
      firstName: user.firstName,
      lastName: user.lastName,
      dob: user.dob,
      doj: user.doj,
      address: user.address,
      contactNo: user.contactNo,
      totalExperience: user.totalExperience
    };

    const isFilled = (field) => {
      if (field === null || field === undefined) return false;
      if (typeof field === 'string') return field.trim() !== '';
      if (typeof field === 'number') return true;
      return false;
    };

    const missingFields = Object.entries(requiredFields).filter(([_, value]) => !isFilled(value));

    if (missingFields.length > 0) {
      return { status: 'Pending', className: styles.pending };
    } else {
      return { status: 'Completed', className: styles.completed };
    }
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    
      <div className={styles.container}>
        <div className={styles.userMaster}>
          <div className={styles.panelHeader}>
            <h2>User Personal Details</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
            </div>
          </div>

          <div className={styles.userTableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Address</th>
                  <th>Contact Info</th>
                  <th>DOB</th>
                  <th>DOJ</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="7" style={{ textAlign: "center", padding: "20px" }}>
                      <div className={styles.loader}></div>
                    </td>
                  </tr>
                ) : users.length > 0 ? (
                  users.map((user, index) => {
                    const { status, className } = getUserStatus(user);
                    return (
                      <tr key={index}>
                        <td>
                          <div className={styles.userInfo}>
                            <img src={avatar} alt="avatar" className={styles.avatar} />
                            {`${user.firstName || ''} ${user.lastName || ''}`.trim()}
                          </div>
                        </td>
                        <td>{user.address || '-'}</td>
                        <td>{user.contactNo || '-'}</td>
                        <td>{user.dob ? formatDate(user.dob) : '--'}</td>
                        <td>{user.doj ? formatDate(user.doj) : '--'}</td>
                        <td>
                          <span className={`${styles.statusBadge} ${className}`}>
                            {status}
                          </span>
                        </td>
                        <td>
                          <div className={styles.actions}>
                            <button 
                              className={styles.editBtn} 
                              onClick={() => handleEditUserClick(user)}
                              title="Edit User"
                            >
                              <FaEdit className={styles.editIcon} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '20px' }}>
                      No users found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default UserPersonalDetails;