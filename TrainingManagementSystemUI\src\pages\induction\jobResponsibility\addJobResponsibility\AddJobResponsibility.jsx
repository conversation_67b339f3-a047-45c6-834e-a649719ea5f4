import React, { useState, useEffect, useCallback, useRef } from 'react';
import styles from './AddJobResposibility.module.css';

import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchUsersNotAssignedJobResponsibility, createJobResponsibility, fetchJobResponsibilityLookupByUserId } from '../../../../services/induction/JobResponsibilityService';

const TOAST_CONFIG = {
  position: "top-right",
  autoClose: 3000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  toastId: 'unique-toast'
};



const AddJobResposibility = () => {
  const navigate = useNavigate();
  const hasFetched = useRef(false);

  const [formData, setFormData] = useState({
    userID: '',
    inductionID: '',
    title: '',
    JobResposibility: '',
    responsibilities: '',
  });

  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [jobLookupData, setJobLookupData] = useState(null);
  const [titleOptions, setTitleOptions] = useState([]);

  const fetchData = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetchUsersNotAssignedJobResponsibility();
      console.log('Fetched user data:', response);

      if (response.header?.errorCount > 0) {
        toast.error(response.header.messages[0].messageText, TOAST_CONFIG);
        return;
      }

      if (response.users) {
        const formattedUsers = response.users.map((u) => ({
          value: u.userID.toString(),
          label: u.userName,
          inductionID: u.inductionID
        }));
        console.log('Formatted users:', formattedUsers);
        setUsers(formattedUsers);
      }
    } catch (error) {
      const msg =
        error.response?.data?.header?.messages?.[0]?.messageText ||
        'Error fetching users. Please try again.';
      toast.error(msg, TOAST_CONFIG);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]);

  // Add useEffect for fetching job lookup data
  useEffect(() => {
    const fetchJobLookup = async () => {
      const userId = sessionStorage.getItem('userId');
      try {
        const response = await fetchJobResponsibilityLookupByUserId(userId);
        
        if (response.header?.errorCount > 0) {
          const errorMsg = response.header.messages[0]?.messageText || 'Failed to fetch job responsibility lookup';
          toast.error(errorMsg, TOAST_CONFIG);
          return;
        }

        if (response.jobResponsibiltyLookup) {
          setJobLookupData(response.jobResponsibiltyLookup);
          // Convert jobLookupData to Select options format without filtering
          const options = response.jobResponsibiltyLookup.map(job => ({
            value: job.title,
            label: job.title,
            description: job.description,
            responsibilities: job.responsibilities
          }));
          setTitleOptions(options);
          console.log('Title options:', options); // Add this to debug
        }
      } catch (error) {
        const errorMsg = error.response?.data?.header?.messages?.[0]?.messageText || 'Error fetching job responsibility lookup';
        toast.error(errorMsg, TOAST_CONFIG);
      }
    };

    fetchJobLookup();
  }, []);

  useEffect(() => {
    if (!hasFetched.current) {
      fetchData();
      hasFetched.current = true;
    }
  }, [fetchData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Update handleSelectChange
  const handleSelectChange = async (selected, field) => {
    if (field === 'userID') {
      setFormData((prev) => ({
        ...prev,
        userID: selected ? selected.value : '',
        inductionID: selected ? selected.inductionID : '',
        title: '',
        JobResposibility: '',
        responsibilities: ''
      }));
    } else if (field === 'title') {
      if (selected) {
        const selectedJob = jobLookupData.find(job => job.title === selected.value);
        if (selectedJob) {
          setFormData(prev => ({
            ...prev,
            title: selectedJob.title,
            JobResposibility: selectedJob.description,
            responsibilities: selectedJob.responsibilities
          }));
        }
      } else {
        setFormData(prev => ({
          ...prev,
          title: '',
          JobResposibility: '',
          responsibilities: ''
        }));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const requiredFields = ['userID', 'title', 'JobResposibility'];

    for (let field of requiredFields) {
      if (!formData[field]) {
        toast.error(`Please fill in the ${field} field.`, TOAST_CONFIG);
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const userData = JSON.parse(sessionStorage.getItem('userData') || '{}');
      const plantID = sessionStorage.getItem('plantID') || '1';

      const payload = {
        inductionID: parseInt(formData.inductionID),
        userID: parseInt(formData.userID),
        jobDescriptionMasterID: 1,
        title: formData.title,
        description: formData.JobResposibility,
        responsibilities: formData.responsibilities || '',
        createdBy: userData.userID?.toString() || '2',
        plantID: plantID,
        electronicSignature: userData.firstName + ' ' + userData.lastName,
        signatureDate: new Date().toISOString()
      };

      console.log('Submitting payload:', payload);
      const response = await createJobResponsibility(payload);
      
      if (response.header?.errorCount > 0) {
        const errorMessage = response.header.messages[0]?.messageText || 'Failed to create job responsibility';
        toast.error(errorMessage, TOAST_CONFIG);
        setIsSubmitting(false);
        return;
      }

      // Only navigate on success, don't show success toast
      if (response.header?.informationCount > 0) {
        setTimeout(() => {
          navigate('/induction/job-Responsibility');
        }, 1500);
      }
    } catch (error) {
      console.error('Error creating job responsibility:', error);
      const errorMessage = error.response?.data?.header?.messages?.[0]?.messageText || 
        'An error occurred while creating job responsibility';
      toast.error(errorMessage, TOAST_CONFIG);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        limit={1}
      />
      
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.formContent}>
            <h3 className={styles.sectionHeading}>Add Job Responsibility</h3>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                User <span className={styles.required}>*</span>
              </label>
              <Select
                name="userID"
                options={users}
                value={users.find((u) => u.value === formData.userID) || null}
                onChange={(selected) => handleSelectChange(selected, 'userID')}
                isClearable
                placeholder="-- Select User --"
                className={styles.reactSelect}
                isLoading={isLoading}
                classNamePrefix="select"
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                Title <span className={styles.required}>*</span>
              </label>
              <Select
                name="title"
                options={titleOptions}
                value={titleOptions.find(opt => opt.value === formData.title) || null}
                onChange={(selected) => handleSelectChange(selected, 'title')}
                isClearable
                isSearchable
                placeholder="-- Select Title --"
                className={styles.reactSelect}
                classNamePrefix="select"
                isDisabled={!jobLookupData}
                noOptionsMessage={() => "No titles available"}
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>
                Description <span className={styles.required}>*</span>
              </label>
              <textarea
                name="JobResposibility"
                value={formData.JobResposibility}
                onChange={handleChange}
                rows="2"
                required
              />
            </div>

            <div className={styles.row}>
              <label className={styles.labelWithPadding}>Responsibilities (Optional)</label>
              <textarea
                name="responsibilities"
                value={formData.responsibilities}
                onChange={handleChange}
                rows="2"
              />
            </div>

            <div className={styles.buttonContainer}>
              <button 
                type="submit" 
                className={styles.primaryBtn}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </button>
              <button 
                type="button" 
                className={styles.cancelBtn} 
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </div>
          </div>
        </form>
      </div>
    </>
  );
};

export default AddJobResposibility;

