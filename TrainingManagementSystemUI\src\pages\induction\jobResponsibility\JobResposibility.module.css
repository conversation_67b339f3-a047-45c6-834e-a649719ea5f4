/* JobResposibility.module.css */

.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.JobResposibility {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
  box-sizing: border-box;
  flex-shrink: 0;
  min-width: 200px;
  max-width: 200px;
}

.addUserBtn {
  background-color: #137688;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
}

.jobTableContainer {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  position: relative;
  margin-bottom: 0;
  scrollbar-width: thin;
  -ms-overflow-style: auto;
  padding-bottom: 10px;
}

.jobTableContainer::-webkit-scrollbar {
  display: block;
  height: 8px;
  width: 8px;
}

.jobTableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.jobTableContainer::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.jobTableContainer::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.jobTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-spacing: 0;
}

.jobTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 12px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
}

.jobTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
}

/* Tooltip styles */
.tooltipWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.tooltipText {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: fixed;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: normal;
  word-wrap: break-word;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  pointer-events: none;
}

.tooltipText::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -12px;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #333;
}

.tooltipWrapper:hover .tooltipText {
  visibility: visible;
  opacity: 1;
}

/* Action buttons */
.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  width: 100%;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  background: transparent;
  border: none;
  padding: 2px;
  cursor: pointer;
  min-width: 60px;
}

.editBtn, .deactivateBtn {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 4px 8px !important;
  margin: 0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
  gap: 4px;
}

.editBtn:focus, .deactivateBtn:focus,
.editBtn:active, .deactivateBtn:active,
.editBtn:hover, .deactivateBtn:hover {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.editBtn span, .deactivateBtn span {
  font-size: 10px;
  color: #4d4b4b;
  white-space: nowrap;
}

.editBtn:disabled, .deactivateBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.editBtn:not(:disabled):hover .editIcon,
.deactivateBtn:not(:disabled):hover .deleteIcon {
  transform: scale(1.1);
}

.editIcon, .deleteIcon {
  font-size: 16px;
  min-width: 20px;
  min-height: 15px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editIcon {
  color: #000000;
}

.deleteIcon {
  color: #dc3545;
}

/* Column widths */
.jobTable th:nth-child(1), .jobTable td:nth-child(1) { /* Name */
  min-width: 150px;
}
.jobTable th:nth-child(2), .jobTable td:nth-child(2) { /* Plant Name */
  min-width: 120px;
}
.jobTable th:nth-child(3), .jobTable td:nth-child(3) { /* Title */
  min-width: 150px;
}
.jobTable th:nth-child(4), .jobTable td:nth-child(4) { /* Description */
  min-width: 200px;
}
.jobTable th:nth-child(5), .jobTable td:nth-child(5) { /* Responsibilities */
  min-width: 200px;
}
.jobTable th:nth-child(6), .jobTable td:nth-child(6) { /* Actions */
  min-width: 100px;
  text-align: center;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  max-width: 400px;
  width: 100%;
  box-sizing: border-box;
  color: #2c3e50;
}

.modalActions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.confirmBtn, .cancelBtn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
}

.confirmBtn {
  background-color: #dc3545;
  color: white;
}

.cancelBtn {
  background-color: #137688;
  color: white;
}

/* Spinner styles */
.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .JobResposibility {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .JobResposibility {
    padding: 15px;
  }
}