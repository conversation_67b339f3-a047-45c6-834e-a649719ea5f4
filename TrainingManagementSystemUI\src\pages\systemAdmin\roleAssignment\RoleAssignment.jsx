import React, { useEffect, useState } from 'react';
import styles from './RoleAssignment.module.css';

import { FaEdit } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { getRoleAssignments } from '../../../services/systemAdmin/RoleAssignmentService';
import { toast, ToastContainer } from 'react-toastify';
import Pagination from '../../../components/pagination/Pagination';

const RoleAssignment = () => {
  const navigate = useNavigate();
  const [roleAssignments, setRoleAssignments] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [totalRecords, setTotalRecords] = useState(0);
  const itemsPerPage = 10;
  const [refreshKey, setRefreshKey] = useState(0);

  // Debounce search term
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page on new search
    }, 500); // 500ms debounce delay

    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Fetch role assignments with search and pagination
  useEffect(() => {
    const fetchAssignments = async () => {
      try {
        setLoading(true);
        const response = await getRoleAssignments(currentPage, itemsPerPage, debouncedSearchTerm);
        
        if (response.header?.errorCount === 0 && Array.isArray(response.roleAssignments)) {
          setRoleAssignments(response.roleAssignments);
          setTotalRecords(response.totalRecord || 0);
        } else {
          const message = response.header?.messages?.[0];
          if (message?.messageLevel?.toLowerCase() === 'warning') {
            toast.warning(message.messageText);
          } else if (message?.messageLevel?.toLowerCase() === 'error') {
            toast.error(message.messageText || 'Failed to load role assignments');
          }
          setRoleAssignments([]);
        }
      } catch (error) {
        console.error('Error fetching role assignments:', error);
        toast.error('Error fetching role assignments. Please try again.');
        setRoleAssignments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAssignments();
  }, [currentPage, debouncedSearchTerm, refreshKey]);

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const paginate = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  const handleEdit = (assignment) => {
    navigate('/system-admin/role-assignment/edit-role-assignment', { state: { assignmentData: assignment } });
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  return (
    <>
      <ToastContainer 
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.plantMaster}>
          <div className={styles.panelHeader}>
            <h2>Role Assignment</h2>
            <div className={styles.controls}>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
              <button
                className={styles.addUserBtn}
                onClick={() => navigate('/system-admin/role-assignment/add-role-assignment')}
              >
                + Add
              </button>
            </div>
          </div>

          <div className={styles.plantTableContainer}>
            <table className={styles.plantTable}>
              <thead>
                <tr>
                  <th>Full Name</th>
                  <th>Role Name</th>
                  <th>Plant Name</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="4" style={{ textAlign: "center", padding: "20px" }}>
                      <div className={styles.loader}></div>
                    </td>
                  </tr>
                ) : roleAssignments.length > 0 ? (
                  roleAssignments.map((assignment, index) => (
                    <tr key={index}>
                      <td>{`${assignment.firstName || ''} ${assignment.lastName || ''}`.trim()}</td>
                      <td>{assignment.roleName}</td>
                      <td>{assignment.plantName}</td>
                      <td>
                        <div className={styles.actions}>
                          <button 
                            className={styles.editBtn} 
                            onClick={() => handleEdit(assignment)}
                            title="Edit Role Assignment"
                          >
                            <FaEdit className={styles.editIcon} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                      No role assignments found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            paginate={paginate}
          />
        </div>
      </div>
    </>
  );
};

export default RoleAssignment;
