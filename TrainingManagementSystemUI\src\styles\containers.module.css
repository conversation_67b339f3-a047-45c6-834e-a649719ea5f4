/* Standard page container */
.pageContainer {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

/* Standard content wrapper */
.contentWrapper {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

/* Standard table container */
.tableContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  margin-bottom: 20px;
  min-height: 0;
  max-height: 100%;
  scrollbar-width: thin;
  scrollbar-color: #127C96 #f1f1f1;
}

.tableContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tableContainer::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 3px;
}

.tableContainer::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

/* Responsive design */
@media (max-width: 768px) {
  .pageContainer {
    padding: 0.5rem;
  }
  
  .contentWrapper {
    margin: 0;
    border-radius: 0;
    height: 100%;
  }
}

@media (max-width: 480px) {
  .pageContainer {
    padding: 0.25rem;
  }
}
