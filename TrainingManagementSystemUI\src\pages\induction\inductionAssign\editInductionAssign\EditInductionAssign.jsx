import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import CustomSelect from "../../../../components/CustomSelect/CustomSelect";
import CustomDatePicker from "../../../../components/CustomDatePicker/CustomDatePicker";
import styles from "./EditInductionAssign.module.css";
import Modal from "../../../../components/common/Modal";
import {
  getInductionAssignmentById,
  updateInductionAssignment,
} from "../../../../services/induction/InductionAssignService";

const EditInductionAssign = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const initialFormDataRef = useRef(null);

  const [formData, setFormData] = useState({
    inductionID: "",
    userID: "",
    userName: "",
    dueDate: "",
    remarks: "",
    inductionStatus: "",
  });

  const [loading, setLoading] = useState(false);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState("");

  // Load data on component mount
  useEffect(() => {
    const loadInductionData = async () => {
      const inductionID = location.state?.inductionData?.inductionID;

      if (inductionID) {
        try {
          const result = await getInductionAssignmentById(inductionID);
          if (result) {
            const initialData = {
              inductionID: result.inductionID,
              userID: result.userID,
              userName: `${result.firstName} ${result.lastName}`,
              dueDate: result.dueDate ? result.dueDate.split("T")[0] : "",
              remarks: result.remarks || "",
              inductionStatus: result.inductionStatus || "",
            };
            setFormData(initialData);
            initialFormDataRef.current = { ...initialData };
          } else {
            toast.error("Failed to load induction assignment details.");
          }
        } catch (error) {
          console.error("Error loading induction assignment:", error);
          toast.error("An error occurred while loading the data.");
        }
      } else {
        toast.error("Invalid induction assignment ID.");
        navigate(-1); 
      }
    };

    loadInductionData();
  }, [location.state, navigate]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error("Reason for change is required.");
      return;
    }

    setLoading(true);

    const payload = {
      inductionID: parseInt(formData.inductionID),
      userID: parseInt(formData.userID),
      modifiedBy: "2", // Using the current user ID
      remarks: formData.remarks,
      inductionStatus: formData.inductionStatus,
      reasonForChange: reasonForChange,
      electronicSignature: "2", // Using the current user ID as signature
      signatureDate: new Date().toISOString(),
      dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : null,
    };

    try {
      const response = await updateInductionAssignment(payload);

      if (response.header?.errorCount === 0) {
        toast.success(
          response.header?.messages?.[0]?.messageText || "Updated successfully."
        );
        setShowReasonModal(false);
        setReasonForChange("");
        setTimeout(() => navigate("/induction/induction-assign"), 1500);
      } else {
        toast.error(
          response.header?.messages?.[0]?.messageText || "Update failed."
        );
      }
    } catch (error) {
      console.error("Error updating induction assignment:", error);
      toast.error("An unexpected error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange("");
  };

  return (
    <>
      <ToastContainer />
      <div className={styles.container}>
        <form className={styles.form} onSubmit={handleSubmit}>
          {loading && (
            <div className="progress mb-3" style={{ height: "3px" }}>
              <div
                className="progress-bar progress-bar-striped progress-bar-animated"
                style={{ width: "100%" }}
              ></div>
            </div>
          )}

          <h3 className={styles.sectionHeading}>Edit Induction Assignment</h3>

          <div className={styles.row}>
            <label>User Name</label>
            <input
              type="text"
              name="userName"
              value={formData.userName}
              readOnly
            />
          </div>

          <div className={styles.row}>
            <label>Due Date</label>
            <CustomDatePicker
              value={formData.dueDate}
              onChange={(date) => setFormData(prev => ({ ...prev, dueDate: date }))}
              minDate={new Date().toISOString().split('T')[0]}
              placeholder="Select due date"
            />
          </div>

          <div className={styles.row}>
            <label>Remarks</label>
            <input
              type="text"
              name="remarks"
              value={formData.remarks}
              onChange={handleChange}
              placeholder="Optional remarks"
            />
          </div>

          <div className={styles.row}>
            <label>Induction Status</label>
            <CustomSelect
              value={formData.inductionStatus}
              onChange={(value) => setFormData(prev => ({ ...prev, inductionStatus: value }))}
              options={["Assigned", "InProgress", "Completed", "Overdue"]}
              placeholder="--Select Status--"
            />
          </div>

          <div className={styles.submitRow}>
            <button type="submit" disabled={loading}>
              {loading ? "Updating..." : "Update"}
            </button>
            <button type="button" onClick={() => navigate(-1)}>
              Cancel
            </button>
          </div>
        </form>
      </div>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the Induction Assignment for "{formData.userName}"</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </>
  );
};

export default EditInductionAssign;