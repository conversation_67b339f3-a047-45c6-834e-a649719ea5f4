/* Container and panel styles */
.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.documentReview {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 20px;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
  box-sizing: border-box;
  flex-shrink: 0;
  min-width: 200px;
  max-width: 200px;
}

.addDocBtn {
  background-color: #137688;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
}

.docTableContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  margin-bottom: 20px;
  min-height: 0;
  max-height: 100%;
  scrollbar-width: thin;
  scrollbar-color: #127C96 #f1f1f1;
}

.docTableContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.docTableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

.tableWrapper {
  flex-grow: 1;
  overflow: visible;
  position: relative;
  min-width: 100%;
}

.docTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-spacing: 0;
}

.docTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 8px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.docTable td {
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Column widths and alignments */
.docTable th:nth-child(1), /* Title */
.docTable td:nth-child(1) {
  width: 180px;
  min-width: 180px;
  text-align: left;
}

.docTable th:nth-child(2), /* Course Code */
.docTable td:nth-child(2) {
  width: 150px;
  min-width: 150px;
  text-align: left;
}

.docTable th:nth-child(3), /* Activity */
.docTable td:nth-child(3) {
  width: 100px;
  min-width: 100px;
  text-align: center;
}

.docTable th:nth-child(4), /* Actions */
.docTable td:nth-child(4) {
  width: 180px;
  min-width: 180px;
  text-align: center; /* Centered Actions column header */
}

.viewBtn {
  background: transparent !important;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  height: 100%;
}

.viewIcon {
  font-size: 18px;
  min-width: 5px;
  min-height: 15px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewIcon {
  color: #2fa0bc;
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  width: 100%;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  min-width: 60px;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
  transition: none;
}

.actionButton span {
  font-size: 10px;
  color: #4d4b4b;
  white-space: nowrap;
}



.editBtn, .deactivateBtn, .viewBtn {
  border: none !important;
  background: transparent !important;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
}

.viewBtn {
  margin-right: 6px;
}

.editBtn {
  margin-left: 10px;
}

.editBtn:disabled, .deactivateBtn:disabled, .viewBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.editIcon,
.deactivateIcon,
.viewIcon {
  font-size: 18px;
  min-width: 20px;
  min-height: 15px;
  cursor: pointer;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editIcon {
  color: #000000;
}

.deactivateIcon {
  color: #dc3545;
}

.viewIcon {
  color: #2795b1;
}

.actionButton:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.actions svg {
  font-size: 16px;
}

.viewModalContent {
  padding: 20px;
}

.viewDetails {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 10px 20px;
  align-items: start;
}

.detailRow {
  display: contents;
}

.detailLabel {
  font-weight: 600;
  color: #333;
  text-align: right;
}

.detailValue {
  color: #555;
  text-align: left;
  word-break: break-word;
}

.paginationContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}

/* Spinner styles */
.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 70px;
}

.statusDraft {
  background-color: #e0e0e0;
  color: #424242;
}

.statusApproved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusReturn {
  background-color: #fff3e0;
  color: #f57c00;
}

.statusReject {
  background-color: #ffebee;
  color: #c62828;
}

.rejectModalContent {
    padding: 0 10px;
}

.rejectModalContent p {
    margin-bottom: 15px;
    font-size: 1rem;
    color: #333;
}

.rejectionRemarks {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 1rem;
    min-height: 80px;
    box-sizing: border-box;
    resize: vertical;
}

.rejectionRemarks:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.25);
}

@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }

  .documentReview {
    padding: 15px;
    border-radius: 0;
    height: 100%;
  }

  .actions svg {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.25rem;
  }

  .documentReview {
    height: 100%;
  }

  .actions svg {
    font-size: 16px;
  }
}

.iconGroup {
  display: flex;
  align-items: center;
  gap: 10px;
}

.activity {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.actionGap {
  width: 10px; /* Adjust as needed for spacing between action groups */
}


.actionDivider {
  width: 1px; /* Vertical line */
  background-color: #ccc; /* Grey color for the divider */
  margin: 0 5px; /* Space around the divider */
  height: 30px; /* Height of the divider */
}