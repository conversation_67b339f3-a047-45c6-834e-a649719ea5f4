import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styles from './editOJT.module.css'; // Use the dedicated CSS module
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { fetchOJTById, updateOJT } from '../../../../services/sopojt-Management/OJTMasterService'; // Import API functions
import Modal from '../../../../components/common/Modal';


const EditOJTMaster = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    ojtCode: '',
    ojtTitle: '',
    activitySteps: '',
    activityDetails: [],
    associatedDocument: null,
    associatedDocumentExtension: '',
    associatedDocumentSize: '',
    evaluationType: '',
    numberOfSets: '',
    expectedDuration: '',
    remarks: '',
  });

  const [loading, setLoading] = useState(true);
  const [showReasonModal, setShowReasonModal] = useState(false); // State to control modal visibility
  const [reasonForChange, setReasonForChange] = useState(''); // State for reason for change input
  const [expectedDurationHours, setExpectedDurationHours] = useState('');
  const [expectedDurationMinutes, setExpectedDurationMinutes] = useState('');
  const initialFormDataRef = useRef(null);

  // Fetch OJT Master data based on ID
  useEffect(() => {
    const loadOjtData = async () => {
      try {
        setLoading(true);
        // Use the actual API function to fetch data
        const data = await fetchOJTById(id); // Replace with actual API call

        // Assuming the fetched data structure matches the form state structure
        // Adjust mapping if necessary based on actual API response structure
        // Correcting access to single OJT object and mapping to form state
        const fetchedData = data?.onJobTrainingMaster; // Access the single object directly

        if (fetchedData) {
             const steps = parseInt(fetchedData.activitySteps) || 0;
             const initialDetails = fetchedData.activityDetails || [];
             const paddedDetails = [...initialDetails];
             while(paddedDetails.length < steps) {
                 paddedDetails.push('');
             }

            const totalMinutes = parseInt(fetchedData.expectedDuration) || 0;
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            setExpectedDurationHours(hours ? String(hours) : '');
            setExpectedDurationMinutes(minutes ? String(minutes) : '');

            setFormData({
              ojtCode: fetchedData.onJobTrainingCode || '',
              ojtTitle: fetchedData.onJobTrainingTitle || '',
              activitySteps: fetchedData.activitySteps ? String(fetchedData.activitySteps) : '',
              activityDetails: paddedDetails,
              // Assuming document details are part of fetched data
              associatedDocument: fetchedData.associatedDocumentPath ? fetchedData.associatedDocumentPath : null, // This might need adaptation if fetching a file object is needed
              associatedDocumentExtension: fetchedData.AssociatedDocumentExtention || '', // Corrected key spelling
              associatedDocumentSize: '', // Size is not in fetch response, might need to handle differently
              evaluationType: fetchedData.evaluationType || '',
              numberOfSets: fetchedData.noOfSet ? String(fetchedData.noOfSet) : '',
              expectedDuration: totalMinutes,
              remarks: fetchedData.remarks || '',
            });
            initialFormDataRef.current = {
              ojtCode: fetchedData.onJobTrainingCode || '',
              ojtTitle: fetchedData.onJobTrainingTitle || '',
              activitySteps: fetchedData.activitySteps ? String(fetchedData.activitySteps) : '',
              activityDetails: [...paddedDetails],
              associatedDocument: fetchedData.associatedDocumentPath ? fetchedData.associatedDocumentPath : null,
              associatedDocumentExtension: fetchedData.AssociatedDocumentExtention || '',
              associatedDocumentSize: '',
              evaluationType: fetchedData.evaluationType || '',
              numberOfSets: fetchedData.noOfSet ? String(fetchedData.noOfSet) : '',
              expectedDuration: totalMinutes,
              remarks: fetchedData.remarks || '',
            };
        } else {
            toast.error('OJT Master data not found or invalid response.'); // More specific error
            // Optionally navigate back if data is not found
             navigate('/document-management/ojt-master');
        }

      } catch (error) {
        console.error('Error fetching OJT Master data:', error);
        toast.error('Failed to load OJT Master data for editing.');
        // Optionally navigate back to the grid on error
         navigate('/document-management/ojt-master');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadOjtData();
    }
  }, [id]); // Re-run effect if ID changes

   // Effect to manage activityDetails array size when activitySteps changes (similar to Add form)
  useEffect(() => {
    const steps = parseInt(formData.activitySteps);
    const currentDetailsCount = formData.activityDetails.length;

    if (!isNaN(steps) && steps > 0) {
      if (steps > currentDetailsCount) {
        setFormData(prev => ({
          ...prev,
          activityDetails: [
            ...prev.activityDetails,
            ...Array(steps - currentDetailsCount).fill('')
          ]
        }));
      }
    } else if (currentDetailsCount > 0 && (isNaN(steps) || steps <= 0)) {
        console.log("Activity steps cleared or invalid, but keeping existing details.");
    } else if (currentDetailsCount === 0 && (isNaN(steps) || steps <= 0)) {
        setFormData(prev => ({ ...prev, activityDetails: [] }));
    }
  }, [formData.activitySteps]); // Depend only on activitySteps

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'activitySteps' || name === 'numberOfSets') {
        setFormData({ ...formData, [name]: value.replace(/[^0-9]/g, '') });
    } else if (name === 'expectedDurationHours') {
        setExpectedDurationHours(value.replace(/[^0-9]/g, ''));
    } else if (name === 'expectedDurationMinutes') {
        setExpectedDurationMinutes(value.replace(/[^0-9]/g, ''));
    } else {
        setFormData({ ...formData, [name]: value });
    }
  };

  const handleActivityDetailChange = (index, value) => {
    const updatedDetails = [...formData.activityDetails];
    updatedDetails[index] = value;
    setFormData({ ...formData, activityDetails: updatedDetails });
  };

  const handleRemoveActivityDetail = (indexToRemove) => {
      setFormData(prev => ({
          ...prev,
          activityDetails: prev.activityDetails.filter((_, index) => index !== indexToRemove)
      }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData({
        ...formData,
        associatedDocument: file,
        associatedDocumentExtension: file.name.split('.').pop() || '',
        associatedDocumentSize: `${(file.size / 1024).toFixed(2)} KB` // Convert bytes to KB
      });
    } else {
       setFormData({
        ...formData,
        associatedDocument: null,
        associatedDocumentExtension: '',
        associatedDocumentSize: '',
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault(); // Prevent default form submission
    const currentData = {
      ...formData,
      activityDetails: [...formData.activityDetails]
    };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }
    // Open the reason modal instead of submitting directly
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    // Add validation for reason for change if needed
    if (!reasonForChange.trim()) {
        toast.error('Reason for change is required.');
        return;
    }

    // Validation: Activity Details count must match Activity Steps
    if (formData.activityDetails.length !== parseInt(formData.activitySteps)) {
      toast.error('Number of Activity Details must match the Activity Steps count.');
      return;
    }

    const modifiedBy = sessionStorage.getItem('userID') || 'system'; // Get user ID from session storage

    console.log('Updating OJT Master:', formData, 'Reason:', reasonForChange);

    try {
        const totalMinutes = (parseInt(expectedDurationHours) || 0) * 60 + (parseInt(expectedDurationMinutes) || 0);
        const res = await updateOJT(id, {
            ...formData,
            expectedDuration: totalMinutes
        }, formData.associatedDocument, modifiedBy, reasonForChange);

        if (res?.header?.errorCount === 0) {
            toast.success(res.header.messages?.[0]?.messageText || 'OJT Master updated successfully!');
            // Close modal and navigate
            setShowReasonModal(false);
            setReasonForChange(''); // Clear the reason input
            // Add a delay before navigating to allow the toast to be seen
            setTimeout(() => {
              navigate('/document-management/ojt-master'); // Redirect after success
            }, 1500); // Delay for 1.5 seconds
        } else {
            toast.error(res?.header?.messages?.[0]?.messageText || 'Failed to update OJT Master.');
        }
    } catch (error) {
        console.error('Error updating OJT Master:', error);
        toast.error('An error occurred while updating OJT Master.');
    }
  };

  const handleCancelUpdate = () => {
    // Close the reason modal without updating
    setShowReasonModal(false);
    setReasonForChange(''); // Clear the reason input
  };

  const handleCancel = () => {
    navigate('/document-management/ojt-master'); // Navigate back to OJT Master grid
  };

  const activityStepsCount = parseInt(formData.activitySteps) || 0;

  if (loading) {
      return <div className={styles.container}><p>Loading OJT Master...</p></div>;
  }

  return (
    <div className={styles.container}>
      <ToastContainer />
      <form className={styles.form} onSubmit={handleSubmit}>
        <div className={styles.formContent}>
          <h3 className={styles.sectionHeading}>On Job Training Master</h3>

          <div className={styles.formGrid}>
            {/* OJT Training Code */}
            <div className={styles.row}>
              <label>OJT Training Code <span className={styles.required}>*</span></label>
              <input
                type="text"
                name="ojtCode"
                value={formData.ojtCode}
                onChange={handleInputChange}
                required
                readOnly
                style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
              />
            </div>

            {/* OJT Training Title */}
            <div className={styles.row}>
              <label>OJT Training Title <span className={styles.required}>*</span></label>
              <input
                type="text"
                name="ojtTitle"
                value={formData.ojtTitle}
                onChange={handleInputChange}
                required
                placeholder="Enter OJT training title"
              />
            </div>

            {/* Activity Steps */}
            <div className={styles.row}>
              <label>Activity Steps <span className={styles.required}>*</span></label>
              <input
                type="text"
                name="activitySteps"
                value={formData.activitySteps}
                onChange={handleInputChange}
                required
                placeholder="Enter number of activity steps"
              />
              {formData.activitySteps !== '' && activityStepsCount <= 0 && (
                <small style={{color: '#e53935'}}>Activity Steps must be a positive number</small>
              )}
            </div>

            {/* Evaluation Type */}
            <div className={styles.row}>
              <label>Evaluation Type <span className={styles.required}>*</span></label>
              <select
                name="evaluationType"
                value={formData.evaluationType}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Evaluation Type</option>
                <option value="NotRequired">Not Required</option>
                <option value="Offline">Offline</option>
                <option value="Online">Online</option>
              </select>
            </div>

            {/* Number of Sets */}
            <div className={styles.row}>
              <label>Number of Set <span className={styles.required}>*</span></label>
              <input
                type="text"
                name="numberOfSets"
                value={formData.numberOfSets}
                onChange={handleInputChange}
                required
                placeholder="Enter number of sets"
              />
              {formData.numberOfSets !== '' && parseInt(formData.numberOfSets) <= 0 && (
                <small style={{color: '#e53935'}}>Number of Sets must be greater than 0</small>
              )}
            </div>

            {/* Expected Duration */}
            <div className={styles.row}>
              <label>Expected Duration</label>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <input
                  type="text"
                  name="expectedDurationHours"
                  value={expectedDurationHours}
                  onChange={handleInputChange}
                  placeholder="hrs"
                  className={styles.durationInput}
                  maxLength={2}
                />
                <span className={styles.durationLabel}>hour</span>
                <input
                  type="text"
                  name="expectedDurationMinutes"
                  value={expectedDurationMinutes}
                  onChange={handleInputChange}
                  placeholder="mins"
                  className={styles.durationInput}
                  maxLength={2}
                />
                <span className={styles.durationLabel}>min</span>
              </div>
            </div>
          </div>

          {/* Dynamic Activity Details Inputs */}
          {formData.activityDetails.length > 0 && (
            <div className={styles.activityDetailsContainer}>
              <h4 style={{ color: '#00376e', marginBottom: '1rem' }}>Activity Details</h4>
              {formData.activityDetails.map((detail, index) => (
                <div key={index} className={styles.row}>
                  <label>Step {index + 1} <span className={styles.required}>*</span></label>
                  <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
                    <textarea
                      value={detail}
                      onChange={(e) => handleActivityDetailChange(index, e.target.value)}
                      required
                      style={{ flex: 1 }}
                    />
                    {/* Show delete button if total fields !== activity steps count */}
                    {formData.activityDetails.length !== activityStepsCount && (
                      <button
                        type="button"
                        onClick={() => handleRemoveActivityDetail(index)}
                        className={styles.removeActivityDetailButton}
                      >
                        Delete
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Associated Document */}
          <div className={styles.row}>
            <label>Associated Document</label>
            <input
              type="file"
              name="associatedDocument"
              onChange={handleFileChange}
              accept=".pdf,.ppt,.pptx,.mp4"
            />
            <small>Accepted formats: PDF, PPT, PPTX, MP4 (Max size: 500MB)</small>
            {/* Display current associated document details if available */}
            {formData.associatedDocument && typeof formData.associatedDocument === 'string' ? (
              <div className={styles.fileDetails}>
                <p>
                  Current File:
                  {formData.ojtCode}
                  {formData.associatedDocumentExtension
                    ? (formData.associatedDocumentExtension.startsWith('.')
                        ? formData.associatedDocumentExtension
                        : '.' + formData.associatedDocumentExtension)
                    : (
                      // fallback: try to extract from path if extension not present
                      (() => {
                        const path = formData.associatedDocument || '';
                        const ext = path.split('.').pop();
                        return ext && ext !== path ? '.' + ext : '';
                      })()
                    )
                  }
                </p>
              </div>
            ) : formData.associatedDocument && typeof formData.associatedDocument === 'object' ? (
              <div className={styles.fileDetails}>
                <p>New File: {formData.associatedDocument.name}</p>
                <p>Type: {formData.associatedDocumentExtension}</p>
                <p>Size: {formData.associatedDocumentSize}</p>
              </div>
            ) : null}
          </div>

          {/* Remarks */}
          <div className={styles.row}>
            <label>Remarks</label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              placeholder="Enter remarks"
            />
          </div>

          <div className={styles.submitRow}>
            <button type="submit" className={styles.primaryBtn}>Update</button>
            <button type="button" className={styles.cancelBtn} onClick={handleCancel}>Cancel</button>
          </div>
        </div>
      </form>

      {/* Reason for Change Modal */}
      {showReasonModal && (
        <Modal
          title="Reason for Change"
          message={
            <div>
              <p>Please provide a reason for updating the OJT "{formData.ojtTitle}" ({formData.ojtCode})</p>
              <div className={styles.reasonInput}>
                <br />
                <textarea
                  id="reasonForChange"
                  value={reasonForChange}
                  onChange={(e) => setReasonForChange(e.target.value)}
                  placeholder="Please provide a reason for this change..."
                  required
                />
              </div>
            </div>
          }
          onConfirm={handleConfirmUpdate}
          onCancel={handleCancelUpdate}
        />
      )}
    </div>
  );
};

export default EditOJTMaster;
