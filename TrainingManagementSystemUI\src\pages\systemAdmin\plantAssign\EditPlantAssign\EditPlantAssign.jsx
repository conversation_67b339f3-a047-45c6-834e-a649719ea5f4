import React, { useEffect, useState, useContext, useRef } from "react";
import styles from "./EditPlantAssign.module.css";
import { useNavigate } from "react-router-dom";
import { PlantAssignContext } from "../../../../context/PlantAssignContext";

import { fetchAllPlants } from '../../../../services/systemAdmin/PlantMasterService';
import { updatePlantAssign } from "../../../../services/systemAdmin/PlantAssignService";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css'; 
import Modal from '../../../../components/common/Modal';

const EditPlantAssign = () => {
  const navigate = useNavigate();
  const { selectedPlantAssignment } = useContext(PlantAssignContext);
  const initialFormDataRef = useRef(null);

  const [formData, setFormData] = useState({
    employeeID: '',
    selectedPlant: [],
  });

  const [plants, setPlants] = useState([]);
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [reasonForChange, setReasonForChange] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchPlants = async () => {
      try {
        const plantsData = await fetchAllPlants();
        const formattedPlants = plantsData?.plants?.map(p => ({
          value: p.plantID,
          label: p.plantName,
        })) || [];
        setPlants(formattedPlants);
        console.log(formattedPlants);
      } catch (error) {
        console.error("Error fetching plants:", error);
        setPlants([]);
      }
    };

    fetchPlants();

    if (selectedPlantAssignment?.employeeID) {
      const initialData = {
        employeeID: selectedPlantAssignment.employeeID || '',
        selectedPlant: Array.isArray(selectedPlantAssignment.plantIDs)
          ? selectedPlantAssignment.plantIDs
          : typeof selectedPlantAssignment.plantIDs === 'string'
          ? selectedPlantAssignment.plantIDs
              .split(',')
              .map(id => parseInt(id))
              .filter(id => !isNaN(id))
          : [],
      };
      setFormData(initialData);
      initialFormDataRef.current = { ...initialData };
    }
  }, [selectedPlantAssignment]);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    const plantID = parseInt(value);
  
    if (name === "selectedPlant" && !isNaN(plantID)) {
      setFormData(prev => ({
        ...prev,
        selectedPlant: checked
          ? [...prev.selectedPlant, plantID]
          : prev.selectedPlant.filter(id => id !== plantID),
      }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
  
    if (!formData.employeeID || formData.selectedPlant.length === 0) {
      toast.error("Please select at least one plant.");
      return;
    }

    // Check if form data has changed
    const currentData = { ...formData };
    if (JSON.stringify(currentData) === JSON.stringify(initialFormDataRef.current)) {
      toast.info('No changes made to update.');
      return;
    }

    // Open the reason modal
    setShowReasonModal(true);
  };

  const handleConfirmUpdate = async () => {
    if (!reasonForChange.trim()) {
      toast.error('Reason for change is required.');
      return;
    }

    setLoading(true);
  
    const payload = {
      plantAssignmentID: selectedPlantAssignment?.plantAssignmentID,
      plantIDs: formData.selectedPlant
        .filter(Boolean)
        .map(String)
        .join(','),
      userId: formData.employeeID,
      modifiedBy: "admin",
      reasonForChange: reasonForChange,
      electronicSignature: "signature_placeholder",
      signatureDate: new Date().toISOString(),
    };
  
    try {
      const response = await updatePlantAssign(payload);
      console.log('API Response:', response);
      console.log('Header Messages:', response.header?.messages);
  
      if (response.header?.errorCount === 0) {
        showMessagesFromHeader(response.header, 'success');

        setTimeout(() => {
          navigate('/system-admin/plant-assignment');
        }, 3000);
      } else {
        showMessagesFromHeader(response.header, 'error');
      }
    } catch (error) {
      console.error('Error updating plant assignment:', error);
    
      const errors = error.response?.data?.errors;
      if (errors) {
        Object.entries(errors).forEach(([field, messages]) => {
          messages.forEach((msg) => {
            toast.error(`${field}: ${msg}`, { autoClose: 3000 });
          });
        });
      } else {
        toast.error("An unexpected error occurred.", { autoClose: 3000 });
      }
    } finally {
      setLoading(false);
      setShowReasonModal(false);
      setReasonForChange('');
    }
  };
  
  const showMessagesFromHeader = (header, type) => {
    if (header?.messages?.length > 0) {
      header.messages.forEach((msg) => {
        if (msg.messageText) {
          if (type === 'success') {
            toast.success(msg.messageText, { autoClose: 3000 });
          } else if (type === 'error') {
            toast.error(msg.messageText, { autoClose: 3000 });
          }
        }
      });
    }
  };

  const handleCancelUpdate = () => {
    setShowReasonModal(false);
    setReasonForChange('');
  };

  return (
    <>
      <ToastContainer 
        position="top-right"
        autoClose={1500}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />

      <div className={styles.container}>
        <div className={styles.form}>
          <h2 className={styles.sectionHeading}>Edit Plant Assignment</h2>
          <form onSubmit={handleSubmit}>
            <div className={styles.row}>
              <label>
                User <span className={styles.required}>*</span>
              </label>
              <div style={{ padding: "10px", backgroundColor: "#f3f3f3", borderRadius: "8px", fontWeight: 500 }}>
                {selectedPlantAssignment?.fullName || "No user selected"}
              </div>
            </div>

            <div className={styles.row}>
              <label>
                Select Plants <span className={styles.required}>*</span>
              </label>
              <div className={styles.checkboxContainer}>
                {plants.map((plant) => (
                  <label key={plant.value} className={styles.roundCheckbox}>
                    <input
                      type="checkbox"
                      name="selectedPlant"
                      value={plant.value}
                      checked={formData.selectedPlant.includes(plant.value)}
                      onChange={handleChange}
                    />
                    <span className={styles.customCheckmark}></span>
                    {plant.label}
                  </label>
                ))}
              </div>
            </div>

            <div className={styles.submitRow}>
              <button type="submit" disabled={loading}>
                {loading ? 'Updating...' : 'Update'}
              </button>
              <button type="button" onClick={() => navigate(-1)}>Cancel</button>
            </div>
          </form>
        </div>

        {/* Reason for Change Modal */}
        {showReasonModal && (
          <Modal
            title="Reason for Change"
            message={
              <div>
                <p>Please provide a reason for updating the plant assignments for user "{selectedPlantAssignment?.fullName}" from "{initialFormDataRef.current?.selectedPlant.map(id => plants.find(p => p.value === id)?.label).filter(Boolean).join(', ') || 'None'}" to "{formData.selectedPlant.map(id => plants.find(p => p.value === id)?.label).filter(Boolean).join(', ') || 'None'}"</p>
                <div className={styles.reasonInput}>
                  <textarea
                    value={reasonForChange}
                    onChange={(e) => setReasonForChange(e.target.value)}
                    placeholder="Please provide a reason for this change..."
                    required
                  />
                </div>
              </div>
            }
            onConfirm={handleConfirmUpdate}
            onCancel={handleCancelUpdate}
          />
        )}
      </div>
    </>
  );
};

export default EditPlantAssign;