/* Container and panel styles */
.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.documentReview {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 20px;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
}

.docTableContainer {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  position: relative;
  margin-bottom: 20px;
  min-height: 0;
  max-height: 100%;
  scrollbar-width: thin;
  scrollbar-color: #127C96 #f1f1f1;
}

.docTableContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.docTableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

.tableWrapper {
  flex-grow: 1;
  overflow: visible;
  position: relative;
  min-width: 100%;
}

.docTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-spacing: 0;
}

.docTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 8px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.docTable td {
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Column widths and alignments */
.docTable th:nth-child(1), /* Title */
.docTable td:nth-child(1) {
  width: 180px;
  min-width: 180px;
  text-align: left;
}

.docTable th:nth-child(2), /* Course Code */
.docTable td:nth-child(2) {
  width: 150px;
  min-width: 150px;
  text-align: left;
}

.docTable th:nth-child(3), /* Department */
.docTable td:nth-child(3) {
  width: 120px;
  min-width: 120px;
  text-align: left;
}

.docTable th:nth-child(4), /* Actions */
.docTable td:nth-child(4) {
  width: 180px;
  min-width: 180px;
  text-align: center;
}

.viewBtn {
  background: transparent !important;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  height: 100%;
}

.viewIcon {
  font-size: 18px;
  min-width: 20px; /* Adjusted min-width */
  min-height: 15px; /* Adjusted min-height */
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewIcon {
  color: #2795b1; /* Matched color with coursecode.module.css */
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px; /* Adjusted gap to match coursecode.module.css */
  height: 100%;
}

.viewBtn, .downloadBtn { /* Assuming downloadBtn might be needed for consistency */
  padding: 0;
  margin: 0;
}

.actionSpacer {
  display: none;
  width: 0;
}

.actionButton {
  display: flex;
  flex-direction: column; /* Changed to column direction */
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0; /* Removed padding */
  cursor: pointer;
  min-width: 60px; /* Added min-width */
  width: auto; /* Ensure width is auto */
  height: auto; /* Ensure height is auto */
  transition: none; /* Removed transition */
}

.actionButton span {
  font-size: 10px; /* Adjusted font size */
  color: #4d4b4b; /* Adjusted color */
  white-space: nowrap;
}

.approveIcon,
.returnIcon,
.rejectIcon { /* Ensured icon styles are consistent */
  font-size: 18px; /* Adjusted font size */
  min-width: 20px; /* Adjusted min-width */
  min-height: 15px; /* Adjusted min-height */
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.approveIcon { color: #2e7d32; }
.returnIcon { color: #f57c00; }
.rejectIcon { color: #c62828; }

.actions svg { /* Adjusted general svg styles */
  font-size: 18px; /* Adjusted font size */
  transition: transform 0.2s ease, color 0.2s ease; /* Added color transition */
}

.actions svg:hover { /* Adjusted hover scale */
  transform: scale(1.1);
}

.actions svg:active { /* Adjusted active scale */
  transform: scale(0.95);
}

.viewModalContent {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0 20px;
}

.detailBox {
  padding: 12px 0;
  display: flex;
  align-items: center;
  margin-left: 40px;
}

.detailBox:last-of-type {
  border-bottom: none;
}

.detailBox h3 {
  margin: 0;
  color: #000;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
  min-width: 120px;
  text-align: left;
  padding-right: 10px;
}

.detailBox span {
  color: #000;
  font-size: 15px;
  font-weight: 700;
  padding: 0 10px;
  min-width: 20px;
  text-align: center;
}

.detailBox p {
  margin: 0;
  color: #000;
  font-size: 13px;
  line-height: 1.5;
  padding-left: 10px;
  min-width: 200px;
}

/* Status specific styles */
.detailBox:has(h3:contains('Status')) p {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #000;
}

/* Status colors */
.detailBox:has(h3:contains('Status')) p:contains('UnderReview') {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Approved') {
  background-color: #e6f4ea;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Returned') {
  background-color: #fff3cd;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Rejected') {
  background-color: #f8d7da;
  border: 1px solid #127C96;
}

.detailBox:has(h3:contains('Status')) p:contains('Draft') {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

/* Undefined status */
.detailBox:has(h3:contains('Status')) p:not(:contains('UnderReview')):not(:contains('Approved')):not(:contains('Returned')):not(:contains('Rejected')):not(:contains('Draft')) {
  background-color: #f0f0f0;
  border: 1px solid #127C96;
}

/* Course Code specific styles */
.detailBox:has(h3:contains('Course Code')) p {
  font-family: 'Segoe UI', sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  letter-spacing: 0.5px;
}

/* Title specific styles */
.detailBox:has(h3:contains('Title')) p {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

/* Department specific styles */
.detailBox:has(h3:contains('Department')) p {
  font-size: 13px;
  color: #34495e;
}

.linkedDocumentsSection {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #127C96;
}

.linkedDocumentsSection h3 {
  margin: 0 0 20px 40px;
  color: #000;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
}

.linkedDocumentsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.documentSection {
  background: #fff;
  border-radius: 6px;
  /* padding: 15px; */
  margin-bottom: 20px;
}

.documentSection h4 {
  margin: 0 0 15px 40px;
  color: #000;
  font-size: 15px;
  font-weight: 700;
  padding: 8px 0 8px 20px;
  border-bottom: 1px solid #127C96;
  font-family: 'Segoe UI', sans-serif;
}

/* Update the selector for SOP section */
.documentSection h4:contains('type: "SOP"') {
  color: #333;
  font-size: 15px;
  font-weight: 700;
  padding-left: 20px;
  margin-left: 40px;
}

/* Update the selector for OJT section */
.documentSection h4:contains('type: "OJT"') {
  color: #333;
  font-size: 15px;
  font-weight: 700;
  padding-left: 20px;
  margin-left: 40px;
}

/* SOP specific styles */
.documentSection:has(h4:contains('type: "SOP"')) .documentName {
  font-size: 12px;
}

.documentSection:has(h4:contains('type: "SOP"')) .documentCode {
  font-size: 11px;
}

.documentSection:has(h4:contains('type: "SOP"')) .documentFrequency {
  font-size: 11px;
}

.linkedDocumentsList {
  list-style: none;
  padding: 0;
  margin: 0 0 0 40px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 300px;
}

.documentItem {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #127C96;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  max-width: 300px;
}

.documentItem:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(18, 124, 150, 0.1);
  border-color: #127C96;
}

.documentInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
  border-left: 1px solid #127C96;
  padding-left: 8px;
}

.documentName {
  color: #000;
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.documentCode {
  color: #000;
  font-size: 12px;
  font-weight: 400;
  margin-left: 4px;
  white-space: nowrap;
}

.documentFrequency {
  color: #000;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 8px;
  background: rgba(18, 124, 150, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
}

.noDocuments {
  color: #000;
  font-style: italic;
  margin: 0;
  padding: 10px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .viewModal {
    width: 95%;
    padding: 15px;
  }

  .documentInfo {
    gap: 3px;
  }

  .linkedDocumentsSection {
    margin-top: 20px;
    padding-top: 15px;
  }
}

.paginationContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}

/* Spinner styles */
.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  min-width: 70px;
}

.statusDraft {
  background-color: #e0e0e0;
  color: #424242;
}

.statusApproved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.statusReturn {
  background-color: #fff3e0;
  color: #f57c00;
}

.statusReject {
  background-color: #ffebee;
  color: #c62828;
}

.rejectModalContent {
    padding: 0 10px;
}

.rejectModalContent p {
    margin-bottom: 15px;
    font-size: 1rem;
    color: #333;
}

.rejectionRemarks {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 1rem;
    min-height: 80px;
    box-sizing: border-box;
    resize: vertical;
}

.rejectionRemarks:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.25);
}

@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }

  .documentReview {
    padding: 15px;
    border-radius: 0;
    height: 100%;
  }

  .actions svg {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.25rem;
  }

  .documentReview {
    height: 100%;
  }

  .actions svg {
    font-size: 16px;
  }
}

.iconGroup {
  display: flex;
  align-items: center;
  gap: 10px;
}

.activity {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.actionGap {
  width: 10px; /* Adjust as needed for spacing between action groups */
}

.actionDivider {
  width: 1px; /* Vertical line */
  background-color: #ccc; /* Grey color for the divider */
  margin: 0 5px; /* Space around the divider */
  height: 30px; /* Height of the divider */
}

/* Adjustments for the actions container to align items */
.actions {
  display: flex;
  justify-content: center;
  align-items: center; /* Vertically align items in the middle */
  gap: 2px; /* Adjusted gap to match coursecode.module.css */
  height: 100%;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.viewModal {
  background: white;
  border-radius: 8px;
  padding: 0 20px 20px 20px;
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  padding-right: 40px; /* Make space for the close button */
}

.modalHeader h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 0.5px;
  font-family: 'Segoe UI', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 50px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleText {
  font-size: 20px;
  font-weight: 700;
}

.codeText {
  font-size: 16px;
  font-weight: 500;
  color: #666;
}

.closeButton {
  position: absolute;
  right: 20px;
  top: 20px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 0;
  line-height: 1;
  z-index: 2;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.closeButton:active {
  transform: scale(0.95);
}

.reasonInput {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.reasonInput label {
  font-weight: 500;
  color: #333;
}

.reasonInput textarea {
  width: 100%;
  min-height: 100px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.reasonInput textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Remove old rejection remarks styles */
.rejectModalContent,
.rejectionRemarks {
  display: none;
}
