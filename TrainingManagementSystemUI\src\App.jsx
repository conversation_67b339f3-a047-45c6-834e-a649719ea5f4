import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import {ToastContainer} from 'react-toastify';
import Layout from './components/layout/Layout';


// Auth & Landing
const Login = lazy(() => import('./pages/login/Login.jsx'));
const Dashboard = lazy(() => import('./components/dashboard/Dashboard.jsx'));
const PlantSelectionLandingPage = lazy(() => import('./pages/landingPage/plantSelectLandingPage/plantSelectLandingPage.jsx'));

// System Admin

// User Master
const UserMaster = lazy(() => import('./pages/systemAdmin/userMaster/UserMaster.jsx'));
const UserMasterAddUser = lazy(() => import('./pages/systemAdmin/userMaster/AddUser/AddUser.jsx'));
const UserMasterEditUser = lazy(() => import('./pages/systemAdmin/userMaster/EditUser/EditUser.jsx'));

// User Personal Details
const UserProfile = lazy(() => import('./pages/systemAdmin/userPersonalDetails/UserPersonalDetails.jsx'));
const UserProfileAddUser = lazy(() => import('./pages/systemAdmin/userPersonalDetails/AddUser/AddUser.jsx'));
const UserProfileEditUser = lazy(() => import('./pages/systemAdmin/userPersonalDetails/EditUser/EditUser.jsx'));

// Password Configuration
const PasswordConfiguration = lazy(() => import('./pages/systemAdmin/passwordConfiguration/PasswordConfiguration.jsx'));

// Change Password
const ChangePassword = lazy(() => import('./pages/systemAdmin/changePassword/ChangePassword.jsx'));

// Department Master
const DepartmentMaster = lazy(() => import('./pages/systemAdmin/departmentMaster/DepartmentMaster.jsx'));
const AddDepartment = lazy(() => import('./pages/systemAdmin/departmentMaster/addDepartmentMaster/AddDepartment.jsx'));
const EditDepartment = lazy(() => import('./pages/systemAdmin/departmentMaster/editDepartmentMaster/EditDepartment.jsx'));

// Designation Master
const DesignationMaster = lazy(() => import('./pages/systemAdmin/designationMaster/DesignationMaster.jsx'));
const AddDesignation = lazy(() => import('./pages/systemAdmin/designationMaster/addDesignationMaster/AddDesignation.jsx'));
const EditDesignation = lazy(() => import('./pages/systemAdmin/designationMaster/editDesignationMaster/EditDesignation.jsx'));

// Role Master
const RoleMaster = lazy(() => import('./pages/systemAdmin/roleMaster/RoleMaster.jsx'));
const AddRole = lazy(() => import('./pages/systemAdmin/roleMaster/addRoleMaster/AddRole.jsx'));
const EditRole = lazy(() => import('./pages/systemAdmin/roleMaster/editRoleMaster/EditRole.jsx'));

// Plant Master
const PlantMaster = lazy(() => import('./pages/systemAdmin/plantMaster/PlantMaster.jsx'));
const AddPlant = lazy(() => import('./pages/systemAdmin/plantMaster/addPlantMaster/AddPlant.jsx'));
const EditPlant = lazy(() => import('./pages/systemAdmin/plantMaster/editPlantMaster/EditPlant.jsx'));

// Plant Assign
const PlantAssign = lazy(() => import('./pages/systemAdmin/plantAssign/PlantAssign.jsx'));
const AddPlantAssign = lazy(() => import('./pages/systemAdmin/plantAssign/AddPlantAssign/AddPlantAssign.jsx'));
const EditPlantAssign = lazy(() => import('./pages/systemAdmin/plantAssign/EditPlantAssign/EditPlantAssign.jsx'));

// Forgot Password
const ForgotPasswordReset = lazy(() => import('./pages/login/ForgotPasswordReset.jsx'));

//Password Reset
const PasswordReset = lazy(() => import('./pages/systemAdmin/passwordReset/PasswordReset.jsx'));

// Induction Assign
const InductionAssign = lazy(() => import('./pages/induction/inductionAssign/InductionAssign.jsx'));
const AddInductionAssign = lazy(() => import('./pages/induction/inductionAssign/addInductionAssign/AddInductionAssign.jsx'));
const EditInductionAssign = lazy(() => import('./pages/induction/inductionAssign/editInductionAssign/EditInductionAssign.jsx'));

//Induction Sign
const InductionSign = lazy(() => import('./pages/induction/inductionSign/InductionSign.jsx'));

// Role Assignment
const RoleAssignment = lazy(() => import('./pages/systemAdmin/roleAssignment/RoleAssignment.jsx'));
const AddRoleAssignment = lazy(() => import('./pages/systemAdmin/roleAssignment/addRoleAssignment/AddRoleAssignment.jsx'));
const EditRoleAssignment = lazy(() => import('./pages/systemAdmin/roleAssignment/editRoleAssignment/EditRoleAssignment.jsx'));

// Profile
const Profile = lazy(() => import('./pages/profile/Profile.jsx'));

// Induction Module
const JobResposibility = lazy(() => import('./pages/induction/jobResponsibility/JobResponsibility.jsx'));
const AddJobResposibility = lazy(() => import('./pages/induction/jobResponsibility/addJobResponsibility/AddJobResponsibility.jsx'));
const EditJobResposibility = lazy(() => import('./pages/induction/jobResponsibility/editJobResponsibility/EditJobResponsibility.jsx'));

// SOP/OJT Management

// Document Registration
const DocumentRegistration = lazy(() => import('./pages/sopojt-Management/documentRegistration/DocumentRegistration.jsx'));
const RegisterDocument = lazy(() => import('./pages/sopojt-Management/documentRegistration/register/RegisterDocument.jsx'));
const EditDocument = lazy(() => import('./pages/sopojt-Management/documentRegistration/update/EditDocument.jsx'));
const QuestionPrepare = lazy(() => import('./pages/sopojt-Management/questionPrepare/questionPrepare.jsx'));
const QuestionPrepareGrid = lazy(() => import('./pages/sopojt-Management/questionPrepare/questionPrepareGrid.jsx'));
const QuestionApprove = lazy(() => import('./pages/sopojt-Management/questionApprove/questionApprove.jsx'));
const EditQuestionPrepare = lazy(() => import('./pages/sopojt-Management/questionPrepare/EditQuestionPrepare.jsx'));


// Document Review & Approval
const DocumentReviewApproval = lazy(() => import('./pages/sopojt-Management/documentReview&Approval/documentReview&Approval.jsx'));



//OJT Master
const OJTMaster = lazy(() => import('./pages/sopojt-Management/OJTMaster/OJTMaster.jsx'));
const OJTApproval = lazy(() => import('./pages/sopojt-Management/OJTApproval/OJTApproval.jsx'));
const AddOJTMaster = lazy(() => import('./pages/sopojt-Management/OJTMaster/AddOJTMaster/addOJT.jsx'));
const EditOJTMaster = lazy(() => import('./pages/sopojt-Management/OJTMaster/EditOJTMaster/editOJT.jsx'));

// Course Code
const CourseCode = lazy(() => import('./pages/course-code/coursecode/coursecode.jsx'));
const CourseCodeApproval = lazy(() => import('./pages/course-code/coursecodeApproval/coursecodeApprove.jsx'));
const CourseCodeAssign = lazy(() => import('./pages/course-code/coursecodeAssignment/coursecodeAssign.jsx'));
const AddCourseCode = lazy(() => import('./pages/course-code/coursecode/Addcoursecode/addCoursecode.jsx'));
const EditCoursecodeAssignment = lazy(() => import('./pages/course-code/coursecodeAssignment/editCoursecodeAssignment/EditCoursecodeAssignment.jsx'));
const AddCoursecodeAssignment = lazy(() => import('./pages/course-code/coursecodeAssignment/addCoursecodeAssignment/AddCoursecodeAssignment.jsx'));
const EditCourseCode = lazy(() => import('./pages/course-code/coursecode/Editcoursecode/editCoursecode.jsx'));
function App() {
  const fallbackSpinner = <div className="spinnerc" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}></div>;

  return (
    <>
      <Routes>
        {/* Public routes without layout */}
        <Route path="/" element={
          <Suspense fallback={fallbackSpinner}>
            <Login />
          </Suspense>
        } />
        <Route path="/password-reset" element={
          <Suspense fallback={fallbackSpinner}>
            <ForgotPasswordReset />
          </Suspense>
        } />
        <Route path="/select-plant" element={
          <Suspense fallback={fallbackSpinner}>
            <PlantSelectionLandingPage />
          </Suspense>
        } />


        {/* Protected routes with layout */}
        <Route element={<Layout />}>
          <Route path="/dashboard" element={
            <Suspense fallback={fallbackSpinner}>
              <Dashboard />
            </Suspense>
          } />

          {/* User Master routes */}
          <Route path='/system-admin/user-master' element={
            <Suspense fallback={fallbackSpinner}>
              <UserMaster/>
            </Suspense>
          }/>
          <Route path="/system-admin/user-master/add-user" element={
            <Suspense fallback={fallbackSpinner}>
              <UserMasterAddUser />
            </Suspense>
          } />
          <Route path="/system-admin/user-master/edit-user" element={
            <Suspense fallback={fallbackSpinner}>
              <UserMasterEditUser />
            </Suspense>
          } />

          {/* Department Master */}
          <Route path='/system-admin/department-master' element={
            <Suspense fallback={fallbackSpinner}>
              <DepartmentMaster/>
            </Suspense>
          }/>
          <Route path='/system-admin/department-master/add-department' element={
            <Suspense fallback={fallbackSpinner}>
              <AddDepartment/>
            </Suspense>
          }/>
          <Route path='/system-admin/department-master/edit-department' element={
            <Suspense fallback={fallbackSpinner}>
              <EditDepartment/>
            </Suspense>
          }/>

          {/* Plant Master */}
          <Route path='/system-admin/plant-master' element={
            <Suspense fallback={fallbackSpinner}>
              <PlantMaster/>
            </Suspense>
          }/>
          <Route path='/system-admin/plant-master/add-plant' element={
            <Suspense fallback={fallbackSpinner}>
              <AddPlant/>
            </Suspense>
          }/>
          <Route path='/system-admin/plant-master/edit-plant' element={
            <Suspense fallback={fallbackSpinner}>
              <EditPlant/>
            </Suspense>
          }/>

          {/* Plant Assign */}
          <Route path='/system-admin/plant-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <PlantAssign/>
            </Suspense>
          }/>
          <Route path='system-admin/plant-assignment/add-plant-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <AddPlantAssign/>
            </Suspense>
          }/>
          <Route path='/system-admin/plant-assignment/edit-plant-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <EditPlantAssign/>
            </Suspense>
          }/>

          {/* Designation Master */}
          <Route path='/system-admin/designation-master' element={
            <Suspense fallback={fallbackSpinner}>
              <DesignationMaster/>
            </Suspense>
          }/>
          <Route path='/system-admin/designation-master/add-designation' element={
            <Suspense fallback={fallbackSpinner}>
              <AddDesignation/>
            </Suspense>
          }/>
          <Route path='/system-admin/designation-master/edit-designation' element={
            <Suspense fallback={fallbackSpinner}>
              <EditDesignation/>
            </Suspense>
          }/>

          {/* Role Master */}
          <Route path='/system-admin/role-master' element={
            <Suspense fallback={fallbackSpinner}>
              <RoleMaster/>
            </Suspense>
          }/>
          <Route path='/system-admin/role-master/add-role' element={
            <Suspense fallback={fallbackSpinner}>
              <AddRole/>
            </Suspense>
          }/>
          <Route path='/system-admin/role-master/edit-role' element={
            <Suspense fallback={fallbackSpinner}>
              <EditRole/>
            </Suspense>
          }/>

          {/* Role Assignment */}
          <Route path='/system-admin/role-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <RoleAssignment/>
            </Suspense>
          }/>
          <Route path='/system-admin/role-assignment/add-role-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <AddRoleAssignment/>
            </Suspense>
          }/>
          <Route path='/system-admin/role-assignment/edit-role-assignment' element={
            <Suspense fallback={fallbackSpinner}>
              <EditRoleAssignment/>
            </Suspense>
          }/>

          {/* User Personal Details Routes */}
          <Route path="/system-admin/user-personal-details" element={
            <Suspense fallback={fallbackSpinner}>
              <UserProfile/>
            </Suspense>
          } />
          <Route path="/system-admin/user-personal-details/add-user" element={
            <Suspense fallback={fallbackSpinner}>
              <UserProfileAddUser/>
            </Suspense>
          } />
          <Route path="/system-admin/user-personal-details/edit-user" element={
            <Suspense fallback={fallbackSpinner}>
              <UserProfileEditUser />
            </Suspense>
          } />

          {/* Password Configuration Routes */}
          <Route path="/system-admin/password-configuration" element={
            <Suspense fallback={fallbackSpinner}>
              <PasswordConfiguration/>
            </Suspense>
          } />

          {/* Change Password */}
          <Route path="/profile/password-change" element={
            <Suspense fallback={fallbackSpinner}>
              <ChangePassword/>
            </Suspense>
          } />

          {/* Forgot Password */}
          <Route path="/password-reset" element={
            <Suspense fallback={fallbackSpinner}>
              <ForgotPasswordReset/>
            </Suspense>
          } />


        {/* Password Reset */}
          <Route path="/system-admin/password-reset" element={
            <Suspense fallback={fallbackSpinner}>
              <PasswordReset/>
            </Suspense>
          } />

          {/* Profile */}
          <Route path="/profile" element={
            <Suspense fallback={fallbackSpinner}>
              <Profile/>
            </Suspense>
          } />

          {/* Induction Assign */}
          <Route path="/induction/induction-assign" element={
            <Suspense fallback={fallbackSpinner}>
              <InductionAssign/>
            </Suspense>
          } />
          <Route path="/induction/induction-assign/add-induction-assign" element={
            <Suspense fallback={fallbackSpinner}>
              <AddInductionAssign/>
            </Suspense>
          } />
          <Route path="/induction/induction-assign/edit-induction-assign" element={
            <Suspense fallback={fallbackSpinner}>
              <EditInductionAssign/>
            </Suspense>
          } />

          {/* Induction Module */}
          <Route path="/induction/job-responsibility" element={
            <Suspense fallback={fallbackSpinner}>
              <JobResposibility />
            </Suspense>
          } />
          <Route path="/induction/job-responsibility/add-job-responsibility" element={
            <Suspense fallback={fallbackSpinner}>
              <AddJobResposibility/>
            </Suspense>
          } />
          <Route path="/induction/job-responsibility/edit-job-responsibility" element={
            <Suspense fallback={fallbackSpinner}>
              <EditJobResposibility />
            </Suspense>
          } />

          {/* Induction Sign */}
          <Route path="/induction/induction-sign" element={
            <Suspense fallback={fallbackSpinner}>
              <InductionSign/>
            </Suspense>
          } />
          {/* Role Assignment */}
          {/* Induction Sign */}
          <Route path="/induction/induction-sign" element={
            <Suspense fallback={fallbackSpinner}>
              <InductionSign/>
            </Suspense>
          } />
          {/* Role Assignment */}


          {/* SOP Module */}
          <Route path="/document-management/document-registration" element={
            <Suspense fallback={fallbackSpinner}>
              <DocumentRegistration/>
            </Suspense>
          } />
          <Route path="/document-management/document-registration/register-document" element={
            <Suspense fallback={fallbackSpinner}>
              <RegisterDocument/>
            </Suspense>
          } />
          <Route path="/document-management/document-registration/edit-document" element={
            <Suspense fallback={fallbackSpinner}>
              <EditDocument/>
            </Suspense>
          } />

          <Route path="/document-management/questioner-preparation" element={
            <Suspense fallback={fallbackSpinner}>
              <QuestionPrepareGrid />
            </Suspense>
          } />
          <Route path="/document-management/questioner-preparation/add" element={
            <Suspense fallback={fallbackSpinner}>
              <QuestionPrepare />
            </Suspense>
          } />
          <Route path="/document-management/questioner-preparation/edit/:id" element={
            <Suspense fallback={fallbackSpinner}>
              <EditQuestionPrepare />
            </Suspense>
          } />
          <Route path="/document-management/questioner-approval" element={
            <Suspense fallback={fallbackSpinner}>
              <QuestionApprove/>
            </Suspense>
          } />

          {/* Document Review & Approval */}
          <Route path="/document-management/document-approval" element={
            <Suspense fallback={fallbackSpinner}>
              <DocumentReviewApproval/>
            </Suspense>
          } />

          {/* OJT Master */}
          <Route path="/document-management/ojt-master" element={
            <Suspense fallback={fallbackSpinner}>
              <OJTMaster />
            </Suspense>
          } />
          <Route path="/document-management/ojt-approval" element={
            <Suspense fallback={fallbackSpinner}>
              <OJTApproval />
            </Suspense>
          } />
          <Route path="/document-management/ojt-master/add-ojt" element={
            <Suspense fallback={fallbackSpinner}>
              <AddOJTMaster />
            </Suspense>
          } />
          <Route path="/document-management/ojt-master/edit-ojt/:id" element={
            <Suspense fallback={fallbackSpinner}>
              <EditOJTMaster />
            </Suspense>
          } />

          {/* Course Code */}
          <Route path="/course-code/course-code-registration" element={
            <Suspense fallback={fallbackSpinner}>
              <CourseCode/>
            </Suspense>
          } />
          <Route path="/course-code/course-code-registration/add" element={
            <Suspense fallback={fallbackSpinner}>
              <AddCourseCode/>
            </Suspense>
          } />
          <Route path="/course-code/course-code-approval" element={
            <Suspense fallback={fallbackSpinner}>
              <CourseCodeApproval/>
            </Suspense>
          } />
          {/* <Route path="/sopojt-management/course-code/course-code-assignment" element={<CourseCodeAssignment/>} /> */}
          <Route path="/course-code/course-code-registration/edit/:id" element={
            <Suspense fallback={fallbackSpinner}>
              <EditCourseCode/>
            </Suspense>
          } />
          <Route path="/course-code/course-code-assignment" element={
            <Suspense fallback={fallbackSpinner}>
              <CourseCodeAssign/>
            </Suspense>
          } />
          <Route path="/course-code/course-code-assignment/add" element={
            <Suspense fallback={fallbackSpinner}>
              <AddCoursecodeAssignment/>
            </Suspense>
          } />
          <Route path="/course-code/course-code-assignment/edit/:id" element={
            <Suspense fallback={fallbackSpinner}>
              <EditCoursecodeAssignment/>
            </Suspense>
          } />
          </Route>
        </Routes>
        <ToastContainer/>
        </>
  );
}

export default App;
