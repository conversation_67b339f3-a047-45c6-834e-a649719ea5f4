.container {
  height: 100%;
  width: 100%;
  padding: 1rem;
  background-color: #f9f9f9;
  box-sizing: border-box;
  overflow: hidden;
}

.documentMaster {
  background: white;
  border-radius: 12px;
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
  color: black;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 20px;
}

.panelHeader h2 {
  font-size: 24px;
  font-weight: 600;
  color: black;
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.searchInput {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 13px;
  width: 200px;
  box-sizing: border-box;
  flex-shrink: 0;
  min-width: 200px;
  max-width: 200px;
}

.addDocBtn {
  background-color: #137688;
  color: white;
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
}

.docTableContainer {
  flex: 1;
  overflow-x: auto;
  overflow-y: auto;
  position: relative;
  margin-bottom: 20px;
  min-height: 0;
  max-height: 100%;
  scrollbar-width: thin;
  scrollbar-color: #127C96 #f1f1f1;
}

.docTableContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.docTableContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb {
  background: #127C96;
  border-radius: 3px;
}

.docTableContainer::-webkit-scrollbar-thumb:hover {
  background: #0d5a6e;
}

.docTableContainer::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.docTableContainer::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.docTableWrapper {
  flex-grow: 1;
  overflow: visible;
  position: relative;
  min-width: 100%;
}

.docTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  border-spacing: 0;
}

.docTable th {
  position: sticky;
  top: 0;
  text-align: left;
  background-color: white;
  z-index: 1;
  font-weight: 600;
  font-size: 15px;
  color: black;
  padding: 15px 12px;
  border-bottom: 2px solid #dee2e6;
  font-family: 'Segoe UI', sans-serif;
  box-sizing: border-box;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  min-height: 50px;
  vertical-align: middle;
}

.docTable td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
  text-align: left;
  box-sizing: border-box;
  height: 60px;
  font-size: 13px;
  font-weight: 400;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
  text-overflow: clip;
  position: relative;
}

/* Status badge styles */
.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  display: inline-block;
  min-width: 80px;
  text-align: center;
}

.statusDraft {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.statusApproved {
  background-color: #e6f4ea;
  color: #1e7e34;
  border: 1px solid #c3e6cb;
}

.statusReturn {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.statusReject {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.statusUnderReview {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

/* Tooltip styles */
.tooltipWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.tooltipText {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: fixed;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: normal;
  word-wrap: break-word;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  pointer-events: none;
}

.tooltipText::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -12px;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #333;
}

.tooltipWrapper:hover .tooltipText {
  visibility: visible;
  opacity: 1;
}

.actionDivider {
  width: 1px; /* Vertical line */
  background-color: #ccc; /* Grey color for the divider */
  margin: 0 5px; /* Space around the divider */
  height: 30px; /* Height of the divider */
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  width: 100%;
}

.actionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  background: transparent;
  border: none;
  padding: 2px;
  cursor: pointer;
  min-width: 60px;
}

.actionButton span {
  font-size: 10px;
  color: #4d4b4b;
  white-space: nowrap;
}

.editIcon, .deactivateIcon, .viewIcon, .downloadIcon {
  font-size: 16px;
  min-width: 20px;
  min-height: 15px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
  background: none !important;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editIcon { color: #000000; }
.deactivateIcon { color: #dc3545; }
.viewIcon { color: #2795b1; }
.downloadIcon { color: #007bff; }

.disabledIcon {
  opacity: 0.5;
  cursor: not-allowed;
}

.editBtn, .deactivateBtn, .viewBtn, .downloadBtn {
  border: none !important;
  background: transparent !important;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
}

.editBtn:disabled,
.deactivateBtn:disabled,
.viewBtn:disabled,
.downloadBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.editBtn:not(:disabled):hover .editIcon,
.deactivateBtn:not(:disabled):hover .deactivateIcon,
.viewBtn:not(:disabled):hover .viewIcon,
.downloadBtn:not(:disabled):hover .downloadIcon {
  transform: scale(1.1);
}

.docTable th:nth-child(1), .docTable td:nth-child(1) {
  min-width: 150px;
}
.docTable th:nth-child(2), .docTable td:nth-child(2) {
  min-width: 80px;
}
.docTable th:nth-child(3), .docTable td:nth-child(3) {
  min-width: 150px;
}
.docTable th:nth-child(4), .docTable td:nth-child(4) {
  min-width: 80px;
  text-align: center;
}
.docTable th:nth-child(5), .docTable td:nth-child(5) {
  min-width: 80px;
  text-align: center;
}
.docTable th:nth-child(6), .docTable td:nth-child(6) {
  min-width: 150px;
  text-align: center;
}

@media (max-width: 768px) {
  .documentMaster {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }

  .documentMaster {
    padding: 15px;
  }
}

.spinnerCell {
  height: 100px;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #127C96;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.actionButton:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.actionButton:disabled span {
  color: #999;
}

.actionButton:disabled svg {
  opacity: 0.5;
}
